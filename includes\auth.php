<?php
/**
 * نظام المصادقة والتحقق
 * Authentication System
 */

require_once '../config/config.php';
require_once '../config/database.php';

class Auth {
    private $db;
    
    public function __construct() {
        $database = new Database();
        $this->db = $database->getConnection();
    }
    
    /**
     * تسجيل دخول المستخدم
     */
    public function login($username, $password) {
        try {
            // التحقق من محاولات تسجيل الدخول
            if ($this->isAccountLocked($username)) {
                return [
                    'success' => false,
                    'message' => 'الحساب مقفل مؤقتاً بسبب محاولات دخول خاطئة متعددة'
                ];
            }
            
            // البحث عن المستخدم
            $sql = "SELECT u.*, o.name as office_name 
                    FROM users u 
                    LEFT JOIN offices o ON u.office_id = o.id 
                    WHERE u.username = ? AND u.is_active = 1";
            
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$username]);
            $user = $stmt->fetch();
            
            if (!$user) {
                $this->recordFailedLogin($username);
                return [
                    'success' => false,
                    'message' => 'اسم المستخدم أو كلمة المرور غير صحيحة'
                ];
            }
            
            // التحقق من كلمة المرور
            if (!password_verify($password, $user['password'])) {
                $this->recordFailedLogin($username);
                return [
                    'success' => false,
                    'message' => 'اسم المستخدم أو كلمة المرور غير صحيحة'
                ];
            }
            
            // تسجيل دخول ناجح
            $this->createSession($user);
            $this->clearFailedLogins($username);
            $this->updateLastLogin($user['id']);
            
            logActivity('تسجيل دخول', 'users', $user['id']);
            
            return [
                'success' => true,
                'message' => 'تم تسجيل الدخول بنجاح',
                'user' => $user
            ];
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => 'حدث خطأ أثناء تسجيل الدخول'
            ];
        }
    }
    
    /**
     * تسجيل خروج المستخدم
     */
    public function logout() {
        if (isset($_SESSION['user_id'])) {
            logActivity('تسجيل خروج', 'users', $_SESSION['user_id']);
        }
        
        session_destroy();
        return true;
    }
    
    /**
     * إنشاء جلسة المستخدم
     */
    private function createSession($user) {
        $_SESSION['user_id'] = $user['id'];
        $_SESSION['username'] = $user['username'];
        $_SESSION['full_name'] = $user['full_name'];
        $_SESSION['user_role'] = $user['role'];
        $_SESSION['office_id'] = $user['office_id'];
        $_SESSION['office_name'] = $user['office_name'];
        $_SESSION['last_activity'] = time();
        $_SESSION['login_time'] = time();
    }
    
    /**
     * التحقق من قفل الحساب
     */
    private function isAccountLocked($username) {
        $sql = "SELECT COUNT(*) as attempts 
                FROM login_attempts 
                WHERE username = ? 
                AND attempt_time > DATE_SUB(NOW(), INTERVAL ? SECOND)";
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$username, LOGIN_LOCKOUT_TIME]);
        $result = $stmt->fetch();
        
        return $result['attempts'] >= MAX_LOGIN_ATTEMPTS;
    }
    
    /**
     * تسجيل محاولة دخول فاشلة
     */
    private function recordFailedLogin($username) {
        // إنشاء جدول محاولات تسجيل الدخول إذا لم يكن موجوداً
        $this->createLoginAttemptsTable();
        
        $sql = "INSERT INTO login_attempts (username, ip_address, attempt_time) 
                VALUES (?, ?, NOW())";
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$username, $_SERVER['REMOTE_ADDR'] ?? '']);
    }
    
    /**
     * مسح محاولات الدخول الفاشلة
     */
    private function clearFailedLogins($username) {
        $sql = "DELETE FROM login_attempts WHERE username = ?";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$username]);
    }
    
    /**
     * تحديث آخر تسجيل دخول
     */
    private function updateLastLogin($userId) {
        $sql = "UPDATE users SET updated_at = NOW() WHERE id = ?";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$userId]);
    }
    
    /**
     * إنشاء جدول محاولات تسجيل الدخول
     */
    private function createLoginAttemptsTable() {
        $sql = "CREATE TABLE IF NOT EXISTS login_attempts (
            id INT AUTO_INCREMENT PRIMARY KEY,
            username VARCHAR(50) NOT NULL,
            ip_address VARCHAR(45),
            attempt_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_username_time (username, attempt_time)
        )";
        
        $this->db->exec($sql);
    }
    
    /**
     * تغيير كلمة المرور
     */
    public function changePassword($userId, $currentPassword, $newPassword) {
        try {
            // التحقق من كلمة المرور الحالية
            $sql = "SELECT password FROM users WHERE id = ?";
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$userId]);
            $user = $stmt->fetch();
            
            if (!$user || !password_verify($currentPassword, $user['password'])) {
                return [
                    'success' => false,
                    'message' => 'كلمة المرور الحالية غير صحيحة'
                ];
            }
            
            // تحديث كلمة المرور
            $hashedPassword = password_hash($newPassword, PASSWORD_DEFAULT);
            $sql = "UPDATE users SET password = ?, updated_at = NOW() WHERE id = ?";
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$hashedPassword, $userId]);
            
            logActivity('تغيير كلمة المرور', 'users', $userId);
            
            return [
                'success' => true,
                'message' => 'تم تغيير كلمة المرور بنجاح'
            ];
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => 'حدث خطأ أثناء تغيير كلمة المرور'
            ];
        }
    }
    
    /**
     * التحقق من صحة المستخدم الحالي
     */
    public function getCurrentUser() {
        if (!isset($_SESSION['user_id'])) {
            return null;
        }
        
        $sql = "SELECT u.*, o.name as office_name 
                FROM users u 
                LEFT JOIN offices o ON u.office_id = o.id 
                WHERE u.id = ? AND u.is_active = 1";
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$_SESSION['user_id']]);
        
        return $stmt->fetch();
    }
    
    /**
     * التحقق من صلاحية المستخدم
     */
    public function hasPermission($permission) {
        if (!isset($_SESSION['user_role'])) {
            return false;
        }
        
        $userRole = $_SESSION['user_role'];
        $permissions = ROLE_PERMISSIONS[$userRole] ?? [];
        
        return in_array($permission, $permissions);
    }
}
?>
