<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام الإدارة المالية - البداية</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .btn {
            display: inline-block;
            padding: 10px 20px;
            margin: 5px;
            background: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 5px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .success { color: green; }
        .error { color: red; }
        .warning { color: orange; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🏦 نظام الإدارة المالية</h1>
        <p>مرحباً بك في نظام الإدارة المالية الشامل</p>
        
        <h2>📋 فحص سريع للنظام:</h2>
        
        <?php
        echo "<h3>1. فحص PHP:</h3>";
        echo "<p class='success'>✅ PHP يعمل بشكل صحيح - الإصدار: " . PHP_VERSION . "</p>";
        
        echo "<h3>2. فحص الإضافات:</h3>";
        $extensions = ['pdo' => 'قاعدة البيانات', 'json' => 'JSON', 'session' => 'الجلسات'];
        foreach ($extensions as $ext => $desc) {
            if (extension_loaded($ext)) {
                echo "<p class='success'>✅ $desc ($ext) - مثبت</p>";
            } else {
                echo "<p class='error'>❌ $desc ($ext) - غير مثبت</p>";
            }
        }
        
        echo "<h3>3. فحص الملفات:</h3>";
        $files = [
            'config/config.php' => 'ملف الإعدادات',
            'config/database.php' => 'ملف قاعدة البيانات',
            'database/schema.sql' => 'هيكل قاعدة البيانات',
            'login.php' => 'صفحة تسجيل الدخول'
        ];
        
        foreach ($files as $file => $desc) {
            if (file_exists($file)) {
                echo "<p class='success'>✅ $desc - موجود</p>";
            } else {
                echo "<p class='warning'>⚠️ $desc - غير موجود</p>";
            }
        }
        
        echo "<h3>4. فحص قاعدة البيانات:</h3>";
        if (file_exists('config/database.php')) {
            echo "<p class='success'>✅ ملف إعدادات قاعدة البيانات موجود</p>";
            
            try {
                // محاولة تضمين الملف بأمان
                ob_start();
                include_once 'config/database.php';
                $output = ob_get_clean();
                
                if (class_exists('Database')) {
                    echo "<p class='success'>✅ فئة قاعدة البيانات محملة بنجاح</p>";
                    
                    try {
                        $db = new Database();
                        $connection = $db->getConnection();
                        if ($connection) {
                            echo "<p class='success'>✅ الاتصال بقاعدة البيانات نجح</p>";
                        } else {
                            echo "<p class='error'>❌ فشل الاتصال بقاعدة البيانات</p>";
                        }
                    } catch (Exception $e) {
                        echo "<p class='error'>❌ خطأ في الاتصال: " . htmlspecialchars($e->getMessage()) . "</p>";
                    }
                } else {
                    echo "<p class='error'>❌ فئة قاعدة البيانات غير موجودة</p>";
                }
            } catch (Exception $e) {
                echo "<p class='error'>❌ خطأ في تحميل ملف قاعدة البيانات: " . htmlspecialchars($e->getMessage()) . "</p>";
            }
        } else {
            echo "<p class='warning'>⚠️ ملف إعدادات قاعدة البيانات غير موجود - يحتاج إعداد</p>";
        }
        ?>
        
        <hr>
        
        <h2>🚀 الخطوات التالية:</h2>
        
        <div style="margin: 20px 0;">
            <a href="info.php" class="btn">📊 معلومات PHP التفصيلية</a>
            <a href="setup.php" class="btn">⚙️ إعداد النظام</a>
            <a href="login.php" class="btn">🔐 تسجيل الدخول</a>
        </div>
        
        <div style="background: #e7f3ff; padding: 15px; border-radius: 5px; margin: 20px 0;">
            <h3>💡 نصائح:</h3>
            <ul>
                <li>إذا كانت جميع الفحوصات خضراء، يمكنك الذهاب مباشرة لتسجيل الدخول</li>
                <li>إذا كان هناك تحذيرات، استخدم "إعداد النظام" أولاً</li>
                <li>بيانات الدخول الافتراضية: admin / password</li>
            </ul>
        </div>
        
        <div style="background: #fff3cd; padding: 15px; border-radius: 5px;">
            <h3>🔧 إذا كنت تواجه مشاكل:</h3>
            <ol>
                <li>تأكد من تشغيل Apache و MySQL في XAMPP</li>
                <li>تأكد من أن المجلد في htdocs</li>
                <li>جرب الرابط: <code>http://localhost/new2/simple.php</code></li>
            </ol>
        </div>
        
        <hr>
        <p style="text-align: center; color: #666;">
            نظام الإدارة المالية الشامل - تم التطوير بواسطة Augment Agent
        </p>
    </div>
</body>
</html>
