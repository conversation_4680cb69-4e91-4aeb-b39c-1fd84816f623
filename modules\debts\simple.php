<?php
/**
 * وحدة إدارة الديون البسيطة - بدون قاعدة بيانات
 */

session_start();

// التحقق من تسجيل الدخول
if (!isset($_SESSION['logged_in']) || $_SESSION['logged_in'] !== true) {
    header('Location: ../../simple-login.php');
    exit();
}

// بيانات وهمية للديون
$debts = [
    [
        'id' => 1,
        'customer_name' => 'أحمد محمد',
        'customer_type' => 'عميل',
        'amount' => 15000,
        'paid_amount' => 5000,
        'remaining_amount' => 10000,
        'due_date' => '2024-02-15',
        'status' => 'جزئي',
        'description' => 'فاتورة مبيعات شهر يناير'
    ],
    [
        'id' => 2,
        'customer_name' => 'شركة النور للتجارة',
        'customer_type' => 'عميل',
        'amount' => 25000,
        'paid_amount' => 0,
        'remaining_amount' => 25000,
        'due_date' => '2024-01-30',
        'status' => 'متأخر',
        'description' => 'توريد مواد خام'
    ],
    [
        'id' => 3,
        'customer_name' => 'فاطمة علي',
        'customer_type' => 'عميل',
        'amount' => 8000,
        'paid_amount' => 8000,
        'remaining_amount' => 0,
        'due_date' => '2024-01-20',
        'status' => 'مدفوع',
        'description' => 'خدمات استشارية'
    ],
    [
        'id' => 4,
        'customer_name' => 'مؤسسة الخليج',
        'customer_type' => 'مورد',
        'amount' => 12000,
        'paid_amount' => 2000,
        'remaining_amount' => 10000,
        'due_date' => '2024-02-28',
        'status' => 'جزئي',
        'description' => 'مستحقات توريد'
    ],
    [
        'id' => 5,
        'customer_name' => 'محمد سالم',
        'customer_type' => 'عميل',
        'amount' => 5500,
        'paid_amount' => 0,
        'remaining_amount' => 5500,
        'due_date' => '2024-02-10',
        'status' => 'معلق',
        'description' => 'فاتورة خدمات'
    ]
];

// دالة لتحديد لون الحالة
function getStatusColor($status) {
    switch ($status) {
        case 'مدفوع': return 'success';
        case 'معلق': return 'warning';
        case 'جزئي': return 'info';
        case 'متأخر': return 'danger';
        default: return 'secondary';
    }
}

// دالة لتحديد لون نوع العميل
function getCustomerTypeColor($type) {
    return $type === 'عميل' ? 'primary' : 'info';
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الديون - نظام الإدارة المالية</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px 0;
            margin-bottom: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .stats-card {
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            text-align: center;
            transition: transform 0.3s ease;
        }
        
        .stats-card:hover {
            transform: translateY(-5px);
        }
        
        .stats-number {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .table-container {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        
        .table th {
            background: #f8f9fa;
            border: none;
            font-weight: 600;
            padding: 15px;
        }
        
        .table td {
            padding: 15px;
            vertical-align: middle;
        }
        
        .btn-action {
            padding: 5px 10px;
            margin: 2px;
            border-radius: 5px;
            text-decoration: none;
            font-size: 0.8rem;
        }
        
        .overdue {
            background-color: #fff5f5 !important;
        }
        
        .breadcrumb {
            background: white;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="container">
            <div class="d-flex justify-content-between align-items-center">
                <h1><i class="fas fa-money-bill-wave me-3"></i>إدارة الديون</h1>
                <div>
                    <a href="../../simple-dashboard.php" class="btn btn-light me-2">
                        <i class="fas fa-home me-1"></i>الرئيسية
                    </a>
                    <a href="../../simple-login.php?logout=1" class="btn btn-outline-light">
                        <i class="fas fa-sign-out-alt me-1"></i>خروج
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <div class="container">
        <!-- Breadcrumb -->
        <nav class="breadcrumb">
            <ol class="breadcrumb mb-0">
                <li class="breadcrumb-item"><a href="../../simple-dashboard.php">الرئيسية</a></li>
                <li class="breadcrumb-item active">إدارة الديون</li>
            </ol>
        </nav>
        
        <!-- إحصائيات سريعة -->
        <div class="row mb-4">
            <div class="col-lg-3 col-md-6">
                <div class="stats-card">
                    <div class="stats-number text-primary"><?php echo count($debts); ?></div>
                    <div>إجمالي الديون</div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="stats-card">
                    <div class="stats-number text-warning">
                        <?php echo count(array_filter($debts, function($d) { return $d['status'] === 'معلق'; })); ?>
                    </div>
                    <div>ديون معلقة</div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="stats-card">
                    <div class="stats-number text-danger">
                        <?php echo count(array_filter($debts, function($d) { return $d['status'] === 'متأخر'; })); ?>
                    </div>
                    <div>ديون متأخرة</div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="stats-card">
                    <div class="stats-number text-success">
                        <?php 
                        $total_remaining = array_sum(array_column($debts, 'remaining_amount'));
                        echo number_format($total_remaining) . ' ر.س';
                        ?>
                    </div>
                    <div>إجمالي المتبقي</div>
                </div>
            </div>
        </div>
        
        <!-- أزرار الإجراءات -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h3><i class="fas fa-list me-2"></i>قائمة الديون</h3>
            <div>
                <button class="btn btn-success" onclick="showAddForm()">
                    <i class="fas fa-plus me-1"></i>إضافة دين جديد
                </button>
                <button class="btn btn-info" onclick="exportData()">
                    <i class="fas fa-download me-1"></i>تصدير
                </button>
                <button class="btn btn-secondary" onclick="printTable()">
                    <i class="fas fa-print me-1"></i>طباعة
                </button>
            </div>
        </div>
        
        <!-- جدول الديون -->
        <div class="table-container">
            <div class="table-responsive">
                <table class="table table-hover" id="debts-table">
                    <thead>
                        <tr>
                            <th>العميل/المورد</th>
                            <th>النوع</th>
                            <th>المبلغ الإجمالي</th>
                            <th>المدفوع</th>
                            <th>المتبقي</th>
                            <th>تاريخ الاستحقاق</th>
                            <th>الحالة</th>
                            <th>الوصف</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($debts as $debt): ?>
                        <tr class="<?php echo $debt['status'] === 'متأخر' ? 'overdue' : ''; ?>">
                            <td>
                                <strong><?php echo htmlspecialchars($debt['customer_name']); ?></strong>
                            </td>
                            <td>
                                <span class="badge bg-<?php echo getCustomerTypeColor($debt['customer_type']); ?>">
                                    <?php echo $debt['customer_type']; ?>
                                </span>
                            </td>
                            <td><?php echo number_format($debt['amount']) . ' ر.س'; ?></td>
                            <td class="text-success"><?php echo number_format($debt['paid_amount']) . ' ر.س'; ?></td>
                            <td>
                                <strong class="<?php echo $debt['remaining_amount'] > 0 ? 'text-danger' : 'text-success'; ?>">
                                    <?php echo number_format($debt['remaining_amount']) . ' ر.س'; ?>
                                </strong>
                            </td>
                            <td>
                                <?php 
                                $due_date = new DateTime($debt['due_date']);
                                echo $due_date->format('Y-m-d');
                                
                                if ($debt['status'] === 'متأخر') {
                                    $today = new DateTime();
                                    $diff = $today->diff($due_date);
                                    echo '<br><small class="text-danger">متأخر ' . $diff->days . ' يوم</small>';
                                }
                                ?>
                            </td>
                            <td>
                                <span class="badge bg-<?php echo getStatusColor($debt['status']); ?>">
                                    <?php echo $debt['status']; ?>
                                </span>
                            </td>
                            <td>
                                <small><?php echo htmlspecialchars(substr($debt['description'], 0, 30)) . '...'; ?></small>
                            </td>
                            <td>
                                <div class="btn-group-vertical btn-group-sm">
                                    <a href="#" class="btn btn-outline-info btn-action" onclick="viewDebt(<?php echo $debt['id']; ?>)">
                                        <i class="fas fa-eye"></i> عرض
                                    </a>
                                    <?php if ($debt['remaining_amount'] > 0): ?>
                                    <a href="#" class="btn btn-outline-success btn-action" onclick="payDebt(<?php echo $debt['id']; ?>)">
                                        <i class="fas fa-money-bill"></i> دفع
                                    </a>
                                    <?php endif; ?>
                                    <a href="#" class="btn btn-outline-primary btn-action" onclick="editDebt(<?php echo $debt['id']; ?>)">
                                        <i class="fas fa-edit"></i> تعديل
                                    </a>
                                </div>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
        
        <!-- معلومات إضافية -->
        <div class="alert alert-success mt-4">
            <h5><i class="fas fa-check-circle me-2"></i>تم تحميل وحدة إدارة الديون بنجاح!</h5>
            <ul class="mb-0">
                <li>✅ هذه بيانات تجريبية لأغراض العرض والاختبار</li>
                <li>✅ الوحدة تعمل بدون قاعدة بيانات</li>
                <li>✅ يمكنك التفاعل مع جميع الأزرار والوظائف</li>
                <li>✅ النظام يدعم تتبع المدفوعات الجزئية والكاملة</li>
            </ul>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function viewDebt(id) {
            alert('عرض تفاصيل الدين رقم: ' + id + '\n\nهذه وظيفة تجريبية. في النظام الحقيقي ستفتح صفحة تفاصيل الدين.');
        }
        
        function payDebt(id) {
            const amount = prompt('أدخل مبلغ الدفع:');
            if (amount && !isNaN(amount) && parseFloat(amount) > 0) {
                alert('تم تسجيل دفعة بمبلغ ' + amount + ' ر.س للدين رقم ' + id + '\n\nهذه وظيفة تجريبية.');
            }
        }
        
        function editDebt(id) {
            alert('تعديل الدين رقم: ' + id + '\n\nهذه وظيفة تجريبية. في النظام الحقيقي ستفتح نموذج التعديل.');
        }
        
        function showAddForm() {
            alert('إضافة دين جديد\n\nهذه وظيفة تجريبية. في النظام الحقيقي ستفتح نموذج إضافة دين جديد.');
        }
        
        function exportData() {
            alert('تصدير البيانات\n\nهذه وظيفة تجريبية. في النظام الحقيقي ستقوم بتصدير البيانات إلى Excel أو PDF.');
        }
        
        function printTable() {
            window.print();
        }
        
        // تحديث الوقت كل ثانية
        setInterval(function() {
            const now = new Date();
            const timeString = now.toLocaleString('ar-SA');
            document.title = 'إدارة الديون - ' + timeString;
        }, 1000);
    </script>
</body>
</html>
