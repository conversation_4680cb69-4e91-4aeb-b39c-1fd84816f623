<?php
/**
 * وحدة إدارة الصندوق - الوارد والصادر
 */

session_start();

// التحقق من تسجيل الدخول
if (!isset($_SESSION['logged_in']) || $_SESSION['logged_in'] !== true) {
    header('Location: ../../simple-login.php');
    exit();
}

// إعدادات العملة (يمكن تغييرها من الإعدادات)
$currency = 'ر.س';
$currency_name = 'ريال سعودي';

// بيانات وهمية لحركات الصندوق
$transactions = [
    [
        'id' => 1,
        'date' => '2024-01-15',
        'time' => '09:30:00',
        'type' => 'income',
        'amount' => 15000,
        'description' => 'دفعة من العميل أحمد محمد',
        'category' => 'مبيعات',
        'reference' => 'INV-001',
        'user' => 'admin'
    ],
    [
        'id' => 2,
        'date' => '2024-01-15',
        'time' => '11:45:00',
        'type' => 'expense',
        'amount' => 2500,
        'description' => 'شراء مواد مكتبية',
        'category' => 'مصروفات إدارية',
        'reference' => 'EXP-001',
        'user' => 'admin'
    ],
    [
        'id' => 3,
        'date' => '2024-01-15',
        'time' => '14:20:00',
        'type' => 'income',
        'amount' => 8000,
        'description' => 'دفعة من شركة النور للتجارة',
        'category' => 'مبيعات',
        'reference' => 'INV-002',
        'user' => 'admin'
    ],
    [
        'id' => 4,
        'date' => '2024-01-15',
        'time' => '16:10:00',
        'type' => 'expense',
        'amount' => 1200,
        'description' => 'فاتورة كهرباء',
        'category' => 'فواتير',
        'reference' => 'BILL-001',
        'user' => 'admin'
    ],
    [
        'id' => 5,
        'date' => '2024-01-14',
        'time' => '10:15:00',
        'type' => 'income',
        'amount' => 12000,
        'description' => 'دفعة نقدية من عميل',
        'category' => 'مبيعات',
        'reference' => 'CASH-001',
        'user' => 'admin'
    ],
    [
        'id' => 6,
        'date' => '2024-01-14',
        'time' => '15:30:00',
        'type' => 'expense',
        'amount' => 3500,
        'description' => 'راتب موظف',
        'category' => 'رواتب',
        'reference' => 'SAL-001',
        'user' => 'admin'
    ]
];

// حساب الإحصائيات
$today = date('Y-m-d');
$total_income = array_sum(array_column(array_filter($transactions, function($t) { return $t['type'] === 'income'; }), 'amount'));
$total_expense = array_sum(array_column(array_filter($transactions, function($t) { return $t['type'] === 'expense'; }), 'amount'));
$balance = $total_income - $total_expense;

$today_income = array_sum(array_column(array_filter($transactions, function($t) use ($today) { 
    return $t['type'] === 'income' && $t['date'] === $today; 
}), 'amount'));

$today_expense = array_sum(array_column(array_filter($transactions, function($t) use ($today) { 
    return $t['type'] === 'expense' && $t['date'] === $today; 
}), 'amount'));

// دالة لتحديد لون نوع المعاملة
function getTransactionColor($type) {
    return $type === 'income' ? 'success' : 'danger';
}

// دالة لتحديد أيقونة نوع المعاملة
function getTransactionIcon($type) {
    return $type === 'income' ? 'fas fa-arrow-down' : 'fas fa-arrow-up';
}

// دالة لتحديد نص نوع المعاملة
function getTransactionText($type) {
    return $type === 'income' ? 'وارد' : 'صادر';
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الصندوق - نظام الإدارة المالية</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .header {
            background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
            color: white;
            padding: 20px 0;
            margin-bottom: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .stats-card {
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            text-align: center;
            transition: transform 0.3s ease;
        }
        
        .stats-card:hover {
            transform: translateY(-5px);
        }
        
        .stats-number {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .transaction-card {
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            margin-bottom: 15px;
            transition: transform 0.3s ease;
        }
        
        .transaction-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 15px rgba(0,0,0,0.15);
        }
        
        .transaction-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }
        
        .transaction-amount {
            font-size: 1.5rem;
            font-weight: bold;
        }
        
        .transaction-details {
            color: #666;
            font-size: 0.9rem;
        }
        
        .breadcrumb {
            background: white;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .balance-positive {
            color: #28a745;
        }
        
        .balance-negative {
            color: #dc3545;
        }
        
        .quick-actions {
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="container">
            <div class="d-flex justify-content-between align-items-center">
                <h1><i class="fas fa-cash-register me-3"></i>إدارة الصندوق</h1>
                <div>
                    <a href="../../simple-dashboard.php" class="btn btn-light me-2">
                        <i class="fas fa-home me-1"></i>الرئيسية
                    </a>
                    <a href="../../simple-login.php?logout=1" class="btn btn-outline-light">
                        <i class="fas fa-sign-out-alt me-1"></i>خروج
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <div class="container">
        <!-- Breadcrumb -->
        <nav class="breadcrumb">
            <ol class="breadcrumb mb-0">
                <li class="breadcrumb-item"><a href="../../simple-dashboard.php">الرئيسية</a></li>
                <li class="breadcrumb-item active">إدارة الصندوق</li>
            </ol>
        </nav>
        
        <!-- إحصائيات سريعة -->
        <div class="row mb-4">
            <div class="col-lg-3 col-md-6">
                <div class="stats-card">
                    <div class="stats-number text-success"><?php echo number_format($total_income); ?> <?php echo $currency; ?></div>
                    <div>إجمالي الوارد</div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="stats-card">
                    <div class="stats-number text-danger"><?php echo number_format($total_expense); ?> <?php echo $currency; ?></div>
                    <div>إجمالي الصادر</div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="stats-card">
                    <div class="stats-number <?php echo $balance >= 0 ? 'balance-positive' : 'balance-negative'; ?>">
                        <?php echo number_format($balance); ?> <?php echo $currency; ?>
                    </div>
                    <div>الرصيد الحالي</div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="stats-card">
                    <div class="stats-number text-info"><?php echo count($transactions); ?></div>
                    <div>إجمالي المعاملات</div>
                </div>
            </div>
        </div>
        
        <!-- إحصائيات اليوم -->
        <div class="row mb-4">
            <div class="col-md-4">
                <div class="stats-card">
                    <div class="stats-number text-success"><?php echo number_format($today_income); ?> <?php echo $currency; ?></div>
                    <div>وارد اليوم</div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="stats-card">
                    <div class="stats-number text-danger"><?php echo number_format($today_expense); ?> <?php echo $currency; ?></div>
                    <div>صادر اليوم</div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="stats-card">
                    <div class="stats-number text-primary"><?php echo number_format($today_income - $today_expense); ?> <?php echo $currency; ?></div>
                    <div>صافي اليوم</div>
                </div>
            </div>
        </div>
        
        <!-- إجراءات سريعة -->
        <div class="quick-actions">
            <h5><i class="fas fa-bolt me-2"></i>إجراءات سريعة</h5>
            <div class="row">
                <div class="col-md-3">
                    <button class="btn btn-success w-100" onclick="addIncome()">
                        <i class="fas fa-plus me-1"></i>إضافة وارد
                    </button>
                </div>
                <div class="col-md-3">
                    <button class="btn btn-danger w-100" onclick="addExpense()">
                        <i class="fas fa-minus me-1"></i>إضافة صادر
                    </button>
                </div>
                <div class="col-md-3">
                    <button class="btn btn-info w-100" onclick="dailyReport()">
                        <i class="fas fa-calendar-day me-1"></i>كشف يومي
                    </button>
                </div>
                <div class="col-md-3">
                    <button class="btn btn-secondary w-100" onclick="fullReport()">
                        <i class="fas fa-file-alt me-1"></i>تقرير شامل
                    </button>
                </div>
            </div>
        </div>
        
        <!-- قائمة المعاملات -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h3><i class="fas fa-list me-2"></i>آخر المعاملات</h3>
            <div>
                <button class="btn btn-outline-primary" onclick="filterTransactions()">
                    <i class="fas fa-filter me-1"></i>تصفية
                </button>
                <button class="btn btn-outline-secondary" onclick="exportTransactions()">
                    <i class="fas fa-download me-1"></i>تصدير
                </button>
            </div>
        </div>
        
        <div class="row">
            <?php foreach (array_reverse($transactions) as $transaction): ?>
            <div class="col-lg-6 col-md-12">
                <div class="transaction-card">
                    <div class="transaction-header">
                        <div class="d-flex align-items-center">
                            <i class="<?php echo getTransactionIcon($transaction['type']); ?> fa-2x text-<?php echo getTransactionColor($transaction['type']); ?> me-3"></i>
                            <div>
                                <span class="badge bg-<?php echo getTransactionColor($transaction['type']); ?>">
                                    <?php echo getTransactionText($transaction['type']); ?>
                                </span>
                                <div class="transaction-amount text-<?php echo getTransactionColor($transaction['type']); ?>">
                                    <?php echo number_format($transaction['amount']); ?> <?php echo $currency; ?>
                                </div>
                            </div>
                        </div>
                        <div class="text-end">
                            <small class="text-muted"><?php echo $transaction['date']; ?></small><br>
                            <small class="text-muted"><?php echo $transaction['time']; ?></small>
                        </div>
                    </div>
                    
                    <div class="transaction-details">
                        <div class="mb-2">
                            <strong>الوصف:</strong> <?php echo htmlspecialchars($transaction['description']); ?>
                        </div>
                        <div class="row">
                            <div class="col-6">
                                <small><strong>الفئة:</strong> <?php echo htmlspecialchars($transaction['category']); ?></small>
                            </div>
                            <div class="col-6">
                                <small><strong>المرجع:</strong> <?php echo htmlspecialchars($transaction['reference']); ?></small>
                            </div>
                        </div>
                        <div class="mt-2">
                            <small><strong>المستخدم:</strong> <?php echo htmlspecialchars($transaction['user']); ?></small>
                        </div>
                    </div>
                    
                    <div class="d-flex justify-content-end mt-3">
                        <div class="btn-group btn-group-sm">
                            <button class="btn btn-outline-info" onclick="viewTransaction(<?php echo $transaction['id']; ?>)">
                                <i class="fas fa-eye"></i> عرض
                            </button>
                            <button class="btn btn-outline-primary" onclick="editTransaction(<?php echo $transaction['id']; ?>)">
                                <i class="fas fa-edit"></i> تعديل
                            </button>
                            <button class="btn btn-outline-danger" onclick="deleteTransaction(<?php echo $transaction['id']; ?>)">
                                <i class="fas fa-trash"></i> حذف
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            <?php endforeach; ?>
        </div>
        
        <!-- معلومات إضافية -->
        <div class="alert alert-info mt-4">
            <h5><i class="fas fa-info-circle me-2"></i>معلومات الصندوق</h5>
            <div class="row">
                <div class="col-md-6">
                    <ul class="mb-0">
                        <li>العملة المستخدمة: <strong><?php echo $currency_name; ?> (<?php echo $currency; ?>)</strong></li>
                        <li>إجمالي المعاملات: <strong><?php echo count($transactions); ?> معاملة</strong></li>
                        <li>آخر معاملة: <strong><?php echo $transactions[0]['date']; ?></strong></li>
                    </ul>
                </div>
                <div class="col-md-6">
                    <ul class="mb-0">
                        <li>الرصيد الحالي: <strong class="<?php echo $balance >= 0 ? 'text-success' : 'text-danger'; ?>"><?php echo number_format($balance); ?> <?php echo $currency; ?></strong></li>
                        <li>حالة الصندوق: <strong class="<?php echo $balance >= 0 ? 'text-success' : 'text-danger'; ?>"><?php echo $balance >= 0 ? 'إيجابي' : 'سالب'; ?></strong></li>
                        <li>تاريخ آخر تحديث: <strong><?php echo date('Y-m-d H:i:s'); ?></strong></li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function addIncome() {
            const amount = prompt('أدخل مبلغ الوارد:');
            const description = prompt('أدخل وصف المعاملة:');
            if (amount && description && !isNaN(amount) && parseFloat(amount) > 0) {
                alert('تم إضافة وارد بمبلغ ' + amount + ' <?php echo $currency; ?>\nالوصف: ' + description + '\n\nهذه وظيفة تجريبية.');
            }
        }
        
        function addExpense() {
            const amount = prompt('أدخل مبلغ الصادر:');
            const description = prompt('أدخل وصف المعاملة:');
            if (amount && description && !isNaN(amount) && parseFloat(amount) > 0) {
                alert('تم إضافة صادر بمبلغ ' + amount + ' <?php echo $currency; ?>\nالوصف: ' + description + '\n\nهذه وظيفة تجريبية.');
            }
        }
        
        function dailyReport() {
            alert('كشف الصندوق اليومي\n\nوارد اليوم: <?php echo number_format($today_income); ?> <?php echo $currency; ?>\nصادر اليوم: <?php echo number_format($today_expense); ?> <?php echo $currency; ?>\nالصافي: <?php echo number_format($today_income - $today_expense); ?> <?php echo $currency; ?>\n\nهذه وظيفة تجريبية.');
        }
        
        function fullReport() {
            alert('التقرير الشامل للصندوق\n\nإجمالي الوارد: <?php echo number_format($total_income); ?> <?php echo $currency; ?>\nإجمالي الصادر: <?php echo number_format($total_expense); ?> <?php echo $currency; ?>\nالرصيد الحالي: <?php echo number_format($balance); ?> <?php echo $currency; ?>\n\nهذه وظيفة تجريبية.');
        }
        
        function viewTransaction(id) {
            alert('عرض تفاصيل المعاملة رقم: ' + id + '\n\nهذه وظيفة تجريبية.');
        }
        
        function editTransaction(id) {
            alert('تعديل المعاملة رقم: ' + id + '\n\nهذه وظيفة تجريبية.');
        }
        
        function deleteTransaction(id) {
            if (confirm('هل أنت متأكد من حذف هذه المعاملة؟')) {
                alert('تم حذف المعاملة رقم: ' + id + '\n\nهذه وظيفة تجريبية.');
            }
        }
        
        function filterTransactions() {
            alert('تصفية المعاملات\n\nيمكنك تصفية المعاملات حسب:\n- التاريخ\n- النوع (وارد/صادر)\n- الفئة\n- المبلغ\n\nهذه وظيفة تجريبية.');
        }
        
        function exportTransactions() {
            alert('تصدير المعاملات\n\nسيتم تصدير المعاملات إلى:\n- Excel\n- PDF\n- CSV\n\nهذه وظيفة تجريبية.');
        }
    </script>
</body>
</html>
