<?php
/**
 * نظام تسجيل دخول بسيط جداً - بدون تعقيدات
 */

// بدء الجلسة
session_start();

$message = '';
$success = false;

// إذا كان المستخدم مسجل دخوله بالفعل
if (isset($_SESSION['logged_in']) && $_SESSION['logged_in'] === true) {
    header('Location: simple-dashboard.php');
    exit();
}

// معالجة تسجيل الدخول
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $username = trim($_POST['username'] ?? '');
    $password = trim($_POST['password'] ?? '');
    
    // بيانات تسجيل الدخول البسيطة (بدون قاعدة بيانات)
    $valid_users = [
        'admin' => 'password',
        'user' => '123456',
        'test' => 'test'
    ];
    
    if (isset($valid_users[$username]) && $valid_users[$username] === $password) {
        // تسجيل دخول ناجح
        $_SESSION['logged_in'] = true;
        $_SESSION['username'] = $username;
        $_SESSION['login_time'] = date('Y-m-d H:i:s');
        
        $success = true;
        $message = "تم تسجيل الدخول بنجاح! سيتم توجيهك خلال 3 ثوان...";
        
        // إعادة توجيه تلقائية
        echo "<script>
            setTimeout(function() {
                window.location.href = 'simple-dashboard.php';
            }, 3000);
        </script>";
        
    } else {
        $message = "اسم المستخدم أو كلمة المرور غير صحيحة!";
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول البسيط</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .login-container {
            background: white;
            padding: 40px;
            border-radius: 20px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
            width: 100%;
            max-width: 400px;
        }
        
        .login-header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .login-header h1 {
            color: #333;
            margin-bottom: 10px;
        }
        
        .login-header p {
            color: #666;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
            color: #333;
        }
        
        input[type="text"], input[type="password"] {
            width: 100%;
            padding: 15px;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            font-size: 16px;
            transition: border-color 0.3s ease;
        }
        
        input[type="text"]:focus, input[type="password"]:focus {
            outline: none;
            border-color: #667eea;
        }
        
        .btn {
            width: 100%;
            padding: 15px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 10px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: transform 0.3s ease;
        }
        
        .btn:hover {
            transform: translateY(-2px);
        }
        
        .message {
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .message.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .message.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .credentials {
            background: #e7f3ff;
            padding: 20px;
            border-radius: 10px;
            margin-top: 20px;
        }
        
        .credentials h3 {
            color: #0c5460;
            margin-bottom: 15px;
            text-align: center;
        }
        
        .credential-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            padding: 8px;
            background: white;
            border-radius: 5px;
        }
        
        .quick-login {
            margin-top: 15px;
            text-align: center;
        }
        
        .quick-btn {
            display: inline-block;
            padding: 8px 15px;
            margin: 5px;
            background: #17a2b8;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            font-size: 14px;
            transition: background 0.3s ease;
        }
        
        .quick-btn:hover {
            background: #138496;
            color: white;
            text-decoration: none;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-header">
            <h1>🔐 تسجيل الدخول</h1>
            <p>نظام بسيط بدون قاعدة بيانات</p>
        </div>
        
        <?php if ($message): ?>
            <div class="message <?php echo $success ? 'success' : 'error'; ?>">
                <?php echo $message; ?>
            </div>
        <?php endif; ?>
        
        <form method="POST">
            <div class="form-group">
                <label for="username">اسم المستخدم:</label>
                <input type="text" id="username" name="username" 
                       value="<?php echo htmlspecialchars($_POST['username'] ?? 'admin'); ?>" 
                       required autofocus>
            </div>
            
            <div class="form-group">
                <label for="password">كلمة المرور:</label>
                <input type="password" id="password" name="password" 
                       value="<?php echo htmlspecialchars($_POST['password'] ?? 'password'); ?>" 
                       required>
            </div>
            
            <button type="submit" class="btn">
                🚀 دخول
            </button>
        </form>
        
        <div class="credentials">
            <h3>📋 بيانات الدخول المتاحة:</h3>
            
            <div class="credential-item">
                <strong>admin</strong>
                <span>password</span>
            </div>
            
            <div class="credential-item">
                <strong>user</strong>
                <span>123456</span>
            </div>
            
            <div class="credential-item">
                <strong>test</strong>
                <span>test</span>
            </div>
            
            <div class="quick-login">
                <p><strong>دخول سريع:</strong></p>
                <a href="?quick=admin" class="quick-btn" onclick="quickLogin('admin', 'password')">admin</a>
                <a href="?quick=user" class="quick-btn" onclick="quickLogin('user', '123456')">user</a>
                <a href="?quick=test" class="quick-btn" onclick="quickLogin('test', 'test')">test</a>
            </div>
        </div>
        
        <?php if ($success): ?>
            <div style="text-align: center; margin-top: 20px;">
                <a href="simple-dashboard.php" style="background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">
                    🎯 دخول إلى لوحة التحكم
                </a>
            </div>
        <?php endif; ?>
    </div>
    
    <script>
        function quickLogin(username, password) {
            document.getElementById('username').value = username;
            document.getElementById('password').value = password;
            document.querySelector('form').submit();
        }
        
        // إذا كان هناك معامل quick في الرابط
        const urlParams = new URLSearchParams(window.location.search);
        const quick = urlParams.get('quick');
        
        if (quick === 'admin') {
            quickLogin('admin', 'password');
        } else if (quick === 'user') {
            quickLogin('user', '123456');
        } else if (quick === 'test') {
            quickLogin('test', 'test');
        }
    </script>
</body>
</html>
