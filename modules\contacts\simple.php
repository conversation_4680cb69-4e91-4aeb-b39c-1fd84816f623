<?php
/**
 * وحدة إدارة جهات الاتصال البسيطة - بدون قاعدة بيانات
 */

session_start();

// التحقق من تسجيل الدخول
if (!isset($_SESSION['logged_in']) || $_SESSION['logged_in'] !== true) {
    header('Location: ../../simple-login.php');
    exit();
}

// بيانات وهمية لجهات الاتصال
$contacts = [
    [
        'id' => 1,
        'name' => 'أحمد محمد علي',
        'type' => 'عميل',
        'phone' => '0501234567',
        'email' => '<EMAIL>',
        'address' => 'الرياض، حي النخيل، شارع الملك فهد',
        'debts_count' => 2,
        'outstanding_amount' => 15000
    ],
    [
        'id' => 2,
        'name' => 'شركة النور للتجارة',
        'type' => 'عميل',
        'phone' => '0112345678',
        'email' => '<EMAIL>',
        'address' => 'جدة، حي الحمراء، طريق الملك عبدالعزيز',
        'debts_count' => 1,
        'outstanding_amount' => 25000
    ],
    [
        'id' => 3,
        'name' => 'فاطمة علي السالم',
        'type' => 'عميل',
        'phone' => '0551234567',
        'email' => '<EMAIL>',
        'address' => 'الدمام، حي الشاطئ، شارع الخليج',
        'debts_count' => 0,
        'outstanding_amount' => 0
    ],
    [
        'id' => 4,
        'name' => 'مؤسسة الخليج للتوريد',
        'type' => 'مورد',
        'phone' => '0123456789',
        'email' => '<EMAIL>',
        'address' => 'الخبر، حي الراكة، شارع الأمير محمد',
        'debts_count' => 1,
        'outstanding_amount' => 12000
    ],
    [
        'id' => 5,
        'name' => 'محمد سالم الأحمد',
        'type' => 'عميل',
        'phone' => '0567890123',
        'email' => '<EMAIL>',
        'address' => 'مكة المكرمة، حي العزيزية، شارع إبراهيم الخليل',
        'debts_count' => 1,
        'outstanding_amount' => 5500
    ],
    [
        'id' => 6,
        'name' => 'شركة التقنية المتقدمة',
        'type' => 'مورد',
        'phone' => '0134567890',
        'email' => '<EMAIL>',
        'address' => 'الرياض، حي العليا، برج المملكة',
        'debts_count' => 0,
        'outstanding_amount' => 0
    ]
];

// دالة لتحديد لون نوع جهة الاتصال
function getContactTypeColor($type) {
    return $type === 'عميل' ? 'primary' : 'info';
}

// دالة لتحديد أيقونة نوع جهة الاتصال
function getContactTypeIcon($type) {
    return $type === 'عميل' ? 'fas fa-user' : 'fas fa-truck';
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة جهات الاتصال - نظام الإدارة المالية</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .header {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 20px 0;
            margin-bottom: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .stats-card {
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            text-align: center;
            transition: transform 0.3s ease;
        }
        
        .stats-card:hover {
            transform: translateY(-5px);
        }
        
        .stats-number {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .contact-card {
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            transition: transform 0.3s ease;
        }
        
        .contact-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 15px rgba(0,0,0,0.15);
        }
        
        .contact-header {
            display: flex;
            justify-content: between;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .contact-name {
            font-size: 1.2rem;
            font-weight: bold;
            color: #333;
        }
        
        .contact-info {
            color: #666;
            margin-bottom: 10px;
        }
        
        .contact-stats {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 15px;
            margin-top: 15px;
        }
        
        .breadcrumb {
            background: white;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .btn-action {
            padding: 5px 10px;
            margin: 2px;
            border-radius: 5px;
            text-decoration: none;
            font-size: 0.8rem;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="container">
            <div class="d-flex justify-content-between align-items-center">
                <h1><i class="fas fa-address-book me-3"></i>إدارة جهات الاتصال</h1>
                <div>
                    <a href="../../simple-dashboard.php" class="btn btn-light me-2">
                        <i class="fas fa-home me-1"></i>الرئيسية
                    </a>
                    <a href="../../simple-login.php?logout=1" class="btn btn-outline-light">
                        <i class="fas fa-sign-out-alt me-1"></i>خروج
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <div class="container">
        <!-- Breadcrumb -->
        <nav class="breadcrumb">
            <ol class="breadcrumb mb-0">
                <li class="breadcrumb-item"><a href="../../simple-dashboard.php">الرئيسية</a></li>
                <li class="breadcrumb-item active">إدارة جهات الاتصال</li>
            </ol>
        </nav>
        
        <!-- إحصائيات سريعة -->
        <div class="row mb-4">
            <div class="col-lg-3 col-md-6">
                <div class="stats-card">
                    <div class="stats-number text-primary"><?php echo count($contacts); ?></div>
                    <div>إجمالي جهات الاتصال</div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="stats-card">
                    <div class="stats-number text-success">
                        <?php echo count(array_filter($contacts, function($c) { return $c['type'] === 'عميل'; })); ?>
                    </div>
                    <div>العملاء</div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="stats-card">
                    <div class="stats-number text-info">
                        <?php echo count(array_filter($contacts, function($c) { return $c['type'] === 'مورد'; })); ?>
                    </div>
                    <div>الموردين</div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="stats-card">
                    <div class="stats-number text-warning">
                        <?php echo count(array_filter($contacts, function($c) { return $c['outstanding_amount'] > 0; })); ?>
                    </div>
                    <div>لديهم ديون</div>
                </div>
            </div>
        </div>
        
        <!-- أزرار الإجراءات -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h3><i class="fas fa-list me-2"></i>قائمة جهات الاتصال</h3>
            <div>
                <button class="btn btn-success" onclick="showAddForm()">
                    <i class="fas fa-plus me-1"></i>إضافة جهة اتصال
                </button>
                <button class="btn btn-info" onclick="exportData()">
                    <i class="fas fa-download me-1"></i>تصدير
                </button>
                <button class="btn btn-secondary" onclick="printContacts()">
                    <i class="fas fa-print me-1"></i>طباعة
                </button>
            </div>
        </div>
        
        <!-- قائمة جهات الاتصال -->
        <div class="row">
            <?php foreach ($contacts as $contact): ?>
            <div class="col-lg-6 col-md-12">
                <div class="contact-card">
                    <div class="contact-header">
                        <div class="d-flex align-items-center">
                            <i class="<?php echo getContactTypeIcon($contact['type']); ?> fa-2x text-<?php echo getContactTypeColor($contact['type']); ?> me-3"></i>
                            <div>
                                <div class="contact-name"><?php echo htmlspecialchars($contact['name']); ?></div>
                                <span class="badge bg-<?php echo getContactTypeColor($contact['type']); ?>">
                                    <?php echo $contact['type']; ?>
                                </span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="contact-info">
                        <div class="row">
                            <div class="col-6">
                                <i class="fas fa-phone text-success me-2"></i>
                                <a href="tel:<?php echo $contact['phone']; ?>" class="text-decoration-none">
                                    <?php echo htmlspecialchars($contact['phone']); ?>
                                </a>
                            </div>
                            <div class="col-6">
                                <i class="fas fa-envelope text-info me-2"></i>
                                <a href="mailto:<?php echo $contact['email']; ?>" class="text-decoration-none">
                                    <?php echo htmlspecialchars($contact['email']); ?>
                                </a>
                            </div>
                        </div>
                        
                        <div class="mt-2">
                            <i class="fas fa-map-marker-alt text-warning me-2"></i>
                            <small><?php echo htmlspecialchars($contact['address']); ?></small>
                        </div>
                    </div>
                    
                    <div class="contact-stats">
                        <div class="row text-center">
                            <div class="col-6">
                                <div class="fw-bold text-primary"><?php echo $contact['debts_count']; ?></div>
                                <small>عدد الديون</small>
                            </div>
                            <div class="col-6">
                                <div class="fw-bold <?php echo $contact['outstanding_amount'] > 0 ? 'text-danger' : 'text-success'; ?>">
                                    <?php echo number_format($contact['outstanding_amount']); ?> ر.س
                                </div>
                                <small>المبلغ المستحق</small>
                            </div>
                        </div>
                    </div>
                    
                    <div class="d-flex justify-content-between mt-3">
                        <div class="btn-group btn-group-sm">
                            <button class="btn btn-outline-info" onclick="viewContact(<?php echo $contact['id']; ?>)">
                                <i class="fas fa-eye"></i> عرض
                            </button>
                            <button class="btn btn-outline-primary" onclick="editContact(<?php echo $contact['id']; ?>)">
                                <i class="fas fa-edit"></i> تعديل
                            </button>
                            <button class="btn btn-outline-success" onclick="addDebt(<?php echo $contact['id']; ?>)">
                                <i class="fas fa-plus"></i> دين
                            </button>
                        </div>
                        
                        <?php if ($contact['outstanding_amount'] > 0): ?>
                            <span class="badge bg-danger">لديه ديون</span>
                        <?php else: ?>
                            <span class="badge bg-success">لا توجد ديون</span>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            <?php endforeach; ?>
        </div>
        
        <!-- معلومات إضافية -->
        <div class="alert alert-success mt-4">
            <h5><i class="fas fa-check-circle me-2"></i>تم تحميل وحدة إدارة جهات الاتصال بنجاح!</h5>
            <ul class="mb-0">
                <li>✅ هذه بيانات تجريبية لأغراض العرض والاختبار</li>
                <li>✅ الوحدة تعمل بدون قاعدة بيانات</li>
                <li>✅ يمكنك التفاعل مع جميع الأزرار والوظائف</li>
                <li>✅ النظام يدعم إدارة العملاء والموردين</li>
            </ul>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function viewContact(id) {
            alert('عرض تفاصيل جهة الاتصال رقم: ' + id + '\n\nهذه وظيفة تجريبية. في النظام الحقيقي ستفتح صفحة تفاصيل جهة الاتصال.');
        }
        
        function editContact(id) {
            alert('تعديل جهة الاتصال رقم: ' + id + '\n\nهذه وظيفة تجريبية. في النظام الحقيقي ستفتح نموذج التعديل.');
        }
        
        function addDebt(id) {
            alert('إضافة دين لجهة الاتصال رقم: ' + id + '\n\nهذه وظيفة تجريبية. في النظام الحقيقي ستفتح نموذج إضافة دين.');
        }
        
        function showAddForm() {
            alert('إضافة جهة اتصال جديدة\n\nهذه وظيفة تجريبية. في النظام الحقيقي ستفتح نموذج إضافة جهة اتصال جديدة.');
        }
        
        function exportData() {
            alert('تصدير البيانات\n\nهذه وظيفة تجريبية. في النظام الحقيقي ستقوم بتصدير البيانات إلى Excel أو PDF.');
        }
        
        function printContacts() {
            window.print();
        }
        
        // تحديث الوقت كل ثانية
        setInterval(function() {
            const now = new Date();
            const timeString = now.toLocaleString('ar-SA');
            document.title = 'إدارة جهات الاتصال - ' + timeString;
        }, 1000);
    </script>
</body>
</html>
