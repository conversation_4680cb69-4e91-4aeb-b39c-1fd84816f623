@echo off
echo ========================================
echo    نظام الإدارة المالية الشامل
echo    Financial Management System
echo ========================================
echo.

REM التحقق من وجود PHP
php --version >nul 2>&1
if %errorlevel% neq 0 (
    echo خطأ: PHP غير مثبت أو غير موجود في PATH
    echo يرجى تثبيت PHP أولاً
    pause
    exit /b 1
)

echo تم العثور على PHP...
php --version

echo.
echo بدء تشغيل الخادم المحلي...
echo.

REM التحقق من وجود ملف الإعداد
if not exist "config\database.php" (
    echo لم يتم العثور على ملف الإعداد
    echo سيتم فتح معالج الإعداد...
    echo.
    start http://localhost:8000/setup.php
) else (
    echo تم العثور على ملف الإعداد
    echo سيتم فتح النظام...
    echo.
    start http://localhost:8000/
)

echo ========================================
echo الخادم يعمل على: http://localhost:8000
echo اضغط Ctrl+C لإيقاف الخادم
echo ========================================
echo.

REM بدء الخادم المحلي
php -S localhost:8000 -t .

pause
