<?php
/**
 * إضافة جهة اتصال جديدة (عميل أو مورد)
 * Add New Contact (Customer or Supplier)
 */

require_once '../../config/config.php';
require_once '../../config/database.php';

// التحقق من الصلاحيات
requirePermission('debts_manage');

$page_title = 'إضافة جهة اتصال جديدة';

$database = new Database();
$error_message = '';
$success_message = '';

// معالجة إرسال النموذج
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    try {
        // التحقق من البيانات
        $name = sanitize($_POST['name'] ?? '');
        $type = sanitize($_POST['type'] ?? '');
        $phone = sanitize($_POST['phone'] ?? '');
        $email = sanitize($_POST['email'] ?? '');
        $address = sanitize($_POST['address'] ?? '');
        
        // التحقق من صحة البيانات
        if (empty($name)) {
            throw new Exception('يرجى إدخال اسم جهة الاتصال');
        }
        
        if (empty($type) || !array_key_exists($type, CONTACT_TYPES)) {
            throw new Exception('يرجى اختيار نوع جهة الاتصال');
        }
        
        // التحقق من صحة البريد الإلكتروني إذا تم إدخاله
        if (!empty($email) && !validateEmail($email)) {
            throw new Exception('البريد الإلكتروني غير صحيح');
        }
        
        // التحقق من عدم تكرار الاسم في نفس المكتب
        $existing_contact = $database->fetch(
            "SELECT id FROM contacts WHERE name = ? AND office_id = ? AND is_active = 1",
            [$name, $_SESSION['office_id']]
        );
        
        if ($existing_contact) {
            throw new Exception('يوجد جهة اتصال بنفس الاسم في هذا المكتب');
        }
        
        // التحقق من عدم تكرار البريد الإلكتروني إذا تم إدخاله
        if (!empty($email)) {
            $existing_email = $database->fetch(
                "SELECT id FROM contacts WHERE email = ? AND is_active = 1",
                [$email]
            );
            
            if ($existing_email) {
                throw new Exception('البريد الإلكتروني مستخدم من قبل جهة اتصال أخرى');
            }
        }
        
        // إدراج جهة الاتصال الجديدة
        $contact_data = [
            'name' => $name,
            'type' => $type,
            'phone' => !empty($phone) ? $phone : null,
            'email' => !empty($email) ? $email : null,
            'address' => !empty($address) ? $address : null,
            'office_id' => $_SESSION['office_id']
        ];
        
        $contact_id = $database->insert('contacts', $contact_data);
        
        // تسجيل النشاط
        logActivity('إضافة جهة اتصال جديدة', 'contacts', $contact_id, null, $contact_data);
        
        $success_message = 'تم إضافة جهة الاتصال بنجاح';
        
        // إعادة توجيه بعد النجاح
        header('Location: index.php?success=' . urlencode($success_message));
        exit();
        
    } catch (Exception $e) {
        $error_message = $e->getMessage();
    }
}

include '../../includes/header.php';
?>

<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-user-plus me-2"></i>
        إضافة جهة اتصال جديدة
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <a href="index.php" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-right me-1"></i>
            العودة للقائمة
        </a>
    </div>
</div>

<?php if ($error_message): ?>
    <div class="alert alert-danger">
        <i class="fas fa-exclamation-triangle me-2"></i>
        <?php echo $error_message; ?>
    </div>
<?php endif; ?>

<?php if ($success_message): ?>
    <div class="alert alert-success">
        <i class="fas fa-check-circle me-2"></i>
        <?php echo $success_message; ?>
    </div>
<?php endif; ?>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-edit me-2"></i>
                    بيانات جهة الاتصال
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" action="">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="name" class="form-label">الاسم <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="name" name="name" 
                                   value="<?php echo htmlspecialchars($_POST['name'] ?? ''); ?>" 
                                   placeholder="أدخل اسم العميل أو المورد" required>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="type" class="form-label">النوع <span class="text-danger">*</span></label>
                            <select class="form-select" id="type" name="type" required>
                                <option value="">اختر النوع</option>
                                <?php foreach (CONTACT_TYPES as $key => $label): ?>
                                    <option value="<?php echo $key; ?>" 
                                            <?php echo (isset($_POST['type']) && $_POST['type'] == $key) ? 'selected' : ''; ?>>
                                        <?php echo $label; ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="phone" class="form-label">رقم الهاتف</label>
                            <input type="tel" class="form-control" id="phone" name="phone" 
                                   value="<?php echo htmlspecialchars($_POST['phone'] ?? ''); ?>" 
                                   placeholder="05xxxxxxxx">
                            <div class="form-text">مثال: 0501234567</div>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="email" class="form-label">البريد الإلكتروني</label>
                            <input type="email" class="form-control" id="email" name="email" 
                                   value="<?php echo htmlspecialchars($_POST['email'] ?? ''); ?>" 
                                   placeholder="<EMAIL>">
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="address" class="form-label">العنوان</label>
                        <textarea class="form-control" id="address" name="address" rows="3" 
                                  placeholder="أدخل العنوان التفصيلي..."><?php echo htmlspecialchars($_POST['address'] ?? ''); ?></textarea>
                    </div>
                    
                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i>
                            حفظ جهة الاتصال
                        </button>
                        <a href="index.php" class="btn btn-outline-secondary">
                            <i class="fas fa-times me-1"></i>
                            إلغاء
                        </a>
                        <button type="button" class="btn btn-outline-info" onclick="clearForm()">
                            <i class="fas fa-eraser me-1"></i>
                            مسح النموذج
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    معلومات مهمة
                </h5>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <h6><i class="fas fa-lightbulb me-2"></i>نصائح:</h6>
                    <ul class="mb-0">
                        <li>الاسم والنوع مطلوبان</li>
                        <li>تأكد من صحة رقم الهاتف</li>
                        <li>البريد الإلكتروني يجب أن يكون صحيحاً</li>
                        <li>يمكن إضافة العنوان لاحقاً</li>
                    </ul>
                </div>
                
                <div class="alert alert-success">
                    <h6><i class="fas fa-users me-2"></i>أنواع جهات الاتصال:</h6>
                    <ul class="mb-0">
                        <li><strong>عميل:</strong> شخص أو شركة تشتري منك</li>
                        <li><strong>مورد:</strong> شخص أو شركة تشتري منها</li>
                    </ul>
                </div>
            </div>
        </div>
        
        <!-- إحصائيات سريعة -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-chart-bar me-2"></i>
                    إحصائيات سريعة
                </h5>
            </div>
            <div class="card-body">
                <?php
                try {
                    $quick_stats = $database->fetch(
                        "SELECT 
                            COUNT(*) as total_contacts,
                            SUM(CASE WHEN type = 'customer' THEN 1 ELSE 0 END) as customers,
                            SUM(CASE WHEN type = 'supplier' THEN 1 ELSE 0 END) as suppliers
                         FROM contacts 
                         WHERE office_id = ? AND is_active = 1",
                        [$_SESSION['office_id']]
                    );
                ?>
                    <div class="d-flex justify-content-between mb-2">
                        <span>إجمالي جهات الاتصال:</span>
                        <strong><?php echo number_format($quick_stats['total_contacts']); ?></strong>
                    </div>
                    <div class="d-flex justify-content-between mb-2">
                        <span>العملاء:</span>
                        <strong class="text-primary"><?php echo number_format($quick_stats['customers']); ?></strong>
                    </div>
                    <div class="d-flex justify-content-between">
                        <span>الموردين:</span>
                        <strong class="text-info"><?php echo number_format($quick_stats['suppliers']); ?></strong>
                    </div>
                <?php
                } catch (Exception $e) {
                    echo '<p class="text-muted">لا يمكن جلب الإحصائيات</p>';
                }
                ?>
            </div>
        </div>
        
        <!-- نماذج سريعة -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-magic me-2"></i>
                    نماذج سريعة
                </h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <button type="button" class="btn btn-outline-primary btn-sm" onclick="fillCustomerTemplate()">
                        <i class="fas fa-user me-1"></i>
                        نموذج عميل
                    </button>
                    <button type="button" class="btn btn-outline-info btn-sm" onclick="fillSupplierTemplate()">
                        <i class="fas fa-truck me-1"></i>
                        نموذج مورد
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// تحسين تجربة المستخدم
document.addEventListener('DOMContentLoaded', function() {
    // التركيز على حقل الاسم
    document.getElementById('name').focus();
    
    // تنسيق رقم الهاتف
    const phoneInput = document.getElementById('phone');
    phoneInput.addEventListener('input', function() {
        // إزالة جميع الأحرف غير الرقمية
        let value = this.value.replace(/\D/g, '');
        
        // تحديد الطول الأقصى
        if (value.length > 10) {
            value = value.substring(0, 10);
        }
        
        this.value = value;
    });
    
    // التحقق من صحة البريد الإلكتروني
    const emailInput = document.getElementById('email');
    emailInput.addEventListener('blur', function() {
        if (this.value && !isValidEmail(this.value)) {
            this.classList.add('is-invalid');
        } else {
            this.classList.remove('is-invalid');
        }
    });
});

// دالة للتحقق من صحة البريد الإلكتروني
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

// دالة لمسح النموذج
function clearForm() {
    if (confirm('هل أنت متأكد من مسح جميع البيانات؟')) {
        document.querySelector('form').reset();
        document.getElementById('name').focus();
    }
}

// نموذج عميل سريع
function fillCustomerTemplate() {
    document.getElementById('type').value = 'customer';
    document.getElementById('name').focus();
}

// نموذج مورد سريع
function fillSupplierTemplate() {
    document.getElementById('type').value = 'supplier';
    document.getElementById('name').focus();
}

// التحقق من النموذج قبل الإرسال
document.querySelector('form').addEventListener('submit', function(e) {
    const name = document.getElementById('name').value.trim();
    const type = document.getElementById('type').value;
    const email = document.getElementById('email').value.trim();
    
    if (!name) {
        alert('يرجى إدخال اسم جهة الاتصال');
        e.preventDefault();
        document.getElementById('name').focus();
        return;
    }
    
    if (!type) {
        alert('يرجى اختيار نوع جهة الاتصال');
        e.preventDefault();
        document.getElementById('type').focus();
        return;
    }
    
    if (email && !isValidEmail(email)) {
        alert('البريد الإلكتروني غير صحيح');
        e.preventDefault();
        document.getElementById('email').focus();
        return;
    }
});
</script>

<?php include '../../includes/footer.php'; ?>
