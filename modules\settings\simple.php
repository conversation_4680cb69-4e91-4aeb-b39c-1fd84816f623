<?php
/**
 * وحدة الإعدادات العامة
 */

session_start();

// التحقق من تسجيل الدخول
if (!isset($_SESSION['logged_in']) || $_SESSION['logged_in'] !== true) {
    header('Location: ../../simple-login.php');
    exit();
}

$message = '';
$success = false;

// الإعدادات الافتراضية
$settings = [
    'company_name' => 'شركة الإدارة المالية المتقدمة',
    'company_address' => 'الرياض، المملكة العربية السعودية',
    'company_phone' => '+966 11 123 4567',
    'company_email' => '<EMAIL>',
    'currency_code' => 'SAR',
    'currency_symbol' => 'ر.س',
    'currency_name' => 'ريال سعودي',
    'date_format' => 'Y-m-d',
    'time_format' => 'H:i:s',
    'timezone' => 'Asia/Riyadh',
    'language' => 'ar',
    'fiscal_year_start' => '01-01',
    'backup_frequency' => 'daily',
    'email_notifications' => 'enabled',
    'sms_notifications' => 'disabled'
];

// العملات المتاحة
$currencies = [
    ['code' => 'SAR', 'symbol' => 'ر.س', 'name' => 'ريال سعودي'],
    ['code' => 'USD', 'symbol' => '$', 'name' => 'دولار أمريكي'],
    ['code' => 'EUR', 'symbol' => '€', 'name' => 'يورو'],
    ['code' => 'GBP', 'symbol' => '£', 'name' => 'جنيه إسترليني'],
    ['code' => 'AED', 'symbol' => 'د.إ', 'name' => 'درهم إماراتي'],
    ['code' => 'KWD', 'symbol' => 'د.ك', 'name' => 'دينار كويتي'],
    ['code' => 'QAR', 'symbol' => 'ر.ق', 'name' => 'ريال قطري'],
    ['code' => 'BHD', 'symbol' => 'د.ب', 'name' => 'دينار بحريني'],
    ['code' => 'OMR', 'symbol' => 'ر.ع', 'name' => 'ريال عماني'],
    ['code' => 'JOD', 'symbol' => 'د.أ', 'name' => 'دينار أردني'],
    ['code' => 'EGP', 'symbol' => 'ج.م', 'name' => 'جنيه مصري']
];

// معالجة حفظ الإعدادات
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    // تحديث الإعدادات (في النظام الحقيقي ستحفظ في قاعدة البيانات)
    $settings['company_name'] = $_POST['company_name'] ?? $settings['company_name'];
    $settings['company_address'] = $_POST['company_address'] ?? $settings['company_address'];
    $settings['company_phone'] = $_POST['company_phone'] ?? $settings['company_phone'];
    $settings['company_email'] = $_POST['company_email'] ?? $settings['company_email'];
    $settings['currency_code'] = $_POST['currency_code'] ?? $settings['currency_code'];
    $settings['date_format'] = $_POST['date_format'] ?? $settings['date_format'];
    $settings['timezone'] = $_POST['timezone'] ?? $settings['timezone'];
    $settings['fiscal_year_start'] = $_POST['fiscal_year_start'] ?? $settings['fiscal_year_start'];
    $settings['backup_frequency'] = $_POST['backup_frequency'] ?? $settings['backup_frequency'];
    $settings['email_notifications'] = isset($_POST['email_notifications']) ? 'enabled' : 'disabled';
    $settings['sms_notifications'] = isset($_POST['sms_notifications']) ? 'enabled' : 'disabled';
    
    // تحديث رمز ووصف العملة
    foreach ($currencies as $currency) {
        if ($currency['code'] === $settings['currency_code']) {
            $settings['currency_symbol'] = $currency['symbol'];
            $settings['currency_name'] = $currency['name'];
            break;
        }
    }
    
    $success = true;
    $message = 'تم حفظ الإعدادات بنجاح!';
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الإعدادات العامة - نظام الإدارة المالية</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .header {
            background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
            color: white;
            padding: 20px 0;
            margin-bottom: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .settings-container {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        .settings-section {
            border-bottom: 1px solid #e9ecef;
            padding-bottom: 25px;
            margin-bottom: 25px;
        }
        
        .settings-section:last-child {
            border-bottom: none;
            margin-bottom: 0;
        }
        
        .section-title {
            color: #495057;
            font-weight: 600;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #e9ecef;
        }
        
        .form-label {
            font-weight: 600;
            color: #495057;
        }
        
        .form-control, .form-select {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            padding: 12px 15px;
        }
        
        .form-control:focus, .form-select:focus {
            border-color: #6c757d;
            box-shadow: 0 0 0 0.2rem rgba(108, 117, 125, 0.25);
        }
        
        .breadcrumb {
            background: white;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .currency-preview {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 15px;
            margin-top: 10px;
        }
        
        .switch {
            position: relative;
            display: inline-block;
            width: 60px;
            height: 34px;
        }
        
        .switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }
        
        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
            border-radius: 34px;
        }
        
        .slider:before {
            position: absolute;
            content: "";
            height: 26px;
            width: 26px;
            left: 4px;
            bottom: 4px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }
        
        input:checked + .slider {
            background-color: #28a745;
        }
        
        input:checked + .slider:before {
            transform: translateX(26px);
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="container">
            <div class="d-flex justify-content-between align-items-center">
                <h1><i class="fas fa-cog me-3"></i>الإعدادات العامة</h1>
                <div>
                    <a href="../../simple-dashboard.php" class="btn btn-light me-2">
                        <i class="fas fa-home me-1"></i>الرئيسية
                    </a>
                    <a href="../../simple-login.php?logout=1" class="btn btn-outline-light">
                        <i class="fas fa-sign-out-alt me-1"></i>خروج
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <div class="container">
        <!-- Breadcrumb -->
        <nav class="breadcrumb">
            <ol class="breadcrumb mb-0">
                <li class="breadcrumb-item"><a href="../../simple-dashboard.php">الرئيسية</a></li>
                <li class="breadcrumb-item active">الإعدادات العامة</li>
            </ol>
        </nav>
        
        <?php if ($message): ?>
            <div class="alert alert-<?php echo $success ? 'success' : 'danger'; ?> alert-dismissible fade show">
                <i class="fas fa-<?php echo $success ? 'check-circle' : 'exclamation-triangle'; ?> me-2"></i>
                <?php echo $message; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>
        
        <form method="POST">
            <!-- معلومات الشركة -->
            <div class="settings-container">
                <div class="settings-section">
                    <h4 class="section-title">
                        <i class="fas fa-building me-2"></i>معلومات الشركة
                    </h4>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="company_name" class="form-label">اسم الشركة</label>
                                <input type="text" class="form-control" id="company_name" name="company_name" 
                                       value="<?php echo htmlspecialchars($settings['company_name']); ?>">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="company_email" class="form-label">البريد الإلكتروني</label>
                                <input type="email" class="form-control" id="company_email" name="company_email" 
                                       value="<?php echo htmlspecialchars($settings['company_email']); ?>">
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="company_phone" class="form-label">رقم الهاتف</label>
                                <input type="text" class="form-control" id="company_phone" name="company_phone" 
                                       value="<?php echo htmlspecialchars($settings['company_phone']); ?>">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="company_address" class="form-label">العنوان</label>
                                <textarea class="form-control" id="company_address" name="company_address" rows="3"><?php echo htmlspecialchars($settings['company_address']); ?></textarea>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- إعدادات العملة -->
                <div class="settings-section">
                    <h4 class="section-title">
                        <i class="fas fa-coins me-2"></i>إعدادات العملة
                    </h4>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="currency_code" class="form-label">العملة الأساسية</label>
                                <select class="form-select" id="currency_code" name="currency_code" onchange="updateCurrencyPreview()">
                                    <?php foreach ($currencies as $currency): ?>
                                    <option value="<?php echo $currency['code']; ?>" 
                                            <?php echo $currency['code'] === $settings['currency_code'] ? 'selected' : ''; ?>>
                                        <?php echo $currency['name']; ?> (<?php echo $currency['symbol']; ?>)
                                    </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="currency-preview">
                                <h6>معاينة العملة:</h6>
                                <div id="currency-preview">
                                    <strong><?php echo $settings['currency_name']; ?></strong><br>
                                    <span class="text-muted">الرمز: <?php echo $settings['currency_symbol']; ?></span><br>
                                    <span class="text-success">مثال: 1,500 <?php echo $settings['currency_symbol']; ?></span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- إعدادات التاريخ والوقت -->
                <div class="settings-section">
                    <h4 class="section-title">
                        <i class="fas fa-calendar me-2"></i>إعدادات التاريخ والوقت
                    </h4>
                    
                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="date_format" class="form-label">تنسيق التاريخ</label>
                                <select class="form-select" id="date_format" name="date_format">
                                    <option value="Y-m-d" <?php echo $settings['date_format'] === 'Y-m-d' ? 'selected' : ''; ?>>2024-01-15</option>
                                    <option value="d/m/Y" <?php echo $settings['date_format'] === 'd/m/Y' ? 'selected' : ''; ?>>15/01/2024</option>
                                    <option value="d-m-Y" <?php echo $settings['date_format'] === 'd-m-Y' ? 'selected' : ''; ?>>15-01-2024</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="timezone" class="form-label">المنطقة الزمنية</label>
                                <select class="form-select" id="timezone" name="timezone">
                                    <option value="Asia/Riyadh" <?php echo $settings['timezone'] === 'Asia/Riyadh' ? 'selected' : ''; ?>>الرياض (GMT+3)</option>
                                    <option value="Asia/Dubai" <?php echo $settings['timezone'] === 'Asia/Dubai' ? 'selected' : ''; ?>>دبي (GMT+4)</option>
                                    <option value="Asia/Kuwait" <?php echo $settings['timezone'] === 'Asia/Kuwait' ? 'selected' : ''; ?>>الكويت (GMT+3)</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="fiscal_year_start" class="form-label">بداية السنة المالية</label>
                                <input type="text" class="form-control" id="fiscal_year_start" name="fiscal_year_start" 
                                       value="<?php echo htmlspecialchars($settings['fiscal_year_start']); ?>" placeholder="01-01">
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- إعدادات النظام -->
                <div class="settings-section">
                    <h4 class="section-title">
                        <i class="fas fa-cogs me-2"></i>إعدادات النظام
                    </h4>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="backup_frequency" class="form-label">تكرار النسخ الاحتياطي</label>
                                <select class="form-select" id="backup_frequency" name="backup_frequency">
                                    <option value="daily" <?php echo $settings['backup_frequency'] === 'daily' ? 'selected' : ''; ?>>يومي</option>
                                    <option value="weekly" <?php echo $settings['backup_frequency'] === 'weekly' ? 'selected' : ''; ?>>أسبوعي</option>
                                    <option value="monthly" <?php echo $settings['backup_frequency'] === 'monthly' ? 'selected' : ''; ?>>شهري</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">الإشعارات</label>
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <span>إشعارات البريد الإلكتروني</span>
                                    <label class="switch">
                                        <input type="checkbox" name="email_notifications" 
                                               <?php echo $settings['email_notifications'] === 'enabled' ? 'checked' : ''; ?>>
                                        <span class="slider"></span>
                                    </label>
                                </div>
                                <div class="d-flex justify-content-between align-items-center">
                                    <span>إشعارات الرسائل النصية</span>
                                    <label class="switch">
                                        <input type="checkbox" name="sms_notifications" 
                                               <?php echo $settings['sms_notifications'] === 'enabled' ? 'checked' : ''; ?>>
                                        <span class="slider"></span>
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- أزرار الحفظ -->
                <div class="text-center">
                    <button type="submit" class="btn btn-success btn-lg me-3">
                        <i class="fas fa-save me-2"></i>حفظ الإعدادات
                    </button>
                    <button type="button" class="btn btn-secondary btn-lg" onclick="resetSettings()">
                        <i class="fas fa-undo me-2"></i>إعادة تعيين
                    </button>
                </div>
            </div>
        </form>
        
        <!-- معلومات إضافية -->
        <div class="alert alert-info">
            <h5><i class="fas fa-info-circle me-2"></i>معلومات مهمة</h5>
            <ul class="mb-0">
                <li>✅ العملة المحددة ستطبق على جميع أجزاء النظام</li>
                <li>✅ تغيير المنطقة الزمنية يؤثر على جميع التواريخ والأوقات</li>
                <li>✅ النسخ الاحتياطي يتم تلقائياً حسب التكرار المحدد</li>
                <li>✅ الإشعارات تساعد في متابعة العمليات المهمة</li>
            </ul>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        const currencies = <?php echo json_encode($currencies); ?>;
        
        function updateCurrencyPreview() {
            const selectedCode = document.getElementById('currency_code').value;
            const currency = currencies.find(c => c.code === selectedCode);
            
            if (currency) {
                document.getElementById('currency-preview').innerHTML = `
                    <strong>${currency.name}</strong><br>
                    <span class="text-muted">الرمز: ${currency.symbol}</span><br>
                    <span class="text-success">مثال: 1,500 ${currency.symbol}</span>
                `;
            }
        }
        
        function resetSettings() {
            if (confirm('هل أنت متأكد من إعادة تعيين جميع الإعدادات إلى القيم الافتراضية؟')) {
                location.reload();
            }
        }
        
        // تحديث معاينة العملة عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            updateCurrencyPreview();
        });
    </script>
</body>
</html>
