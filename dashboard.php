<?php
/**
 * لوحة التحكم البسيطة
 */

// بدء الجلسة
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit();
}

$user_id = $_SESSION['user_id'];
$username = $_SESSION['username'] ?? 'غير محدد';
$full_name = $_SESSION['full_name'] ?? 'غير محدد';
$user_role = $_SESSION['user_role'] ?? 'غير محدد';
$office_id = $_SESSION['office_id'] ?? 'غير محدد';
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة التحكم - نظام الإدارة المالية</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .dashboard-container {
            max-width: 1200px;
            margin: 20px auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .dashboard-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
        }
        .dashboard-body {
            padding: 30px;
        }
        .stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            text-align: center;
        }
        .stats-number {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .stats-label {
            opacity: 0.8;
            font-size: 0.9rem;
        }
        .feature-card {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            border-left: 4px solid #667eea;
            transition: transform 0.3s ease;
        }
        .feature-card:hover {
            transform: translateY(-2px);
        }
        .btn {
            border-radius: 10px;
            padding: 12px 20px;
            margin: 5px;
        }
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
        }
    </style>
</head>
<body>
    <div class="dashboard-container">
        <div class="dashboard-header">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1><i class="fas fa-chart-line me-3"></i>نظام الإدارة المالية</h1>
                    <p class="mb-0">مرحباً بك في لوحة التحكم</p>
                </div>
                <div>
                    <a href="logout.php" class="btn btn-light">
                        <i class="fas fa-sign-out-alt me-2"></i>
                        تسجيل الخروج
                    </a>
                </div>
            </div>
        </div>
        
        <div class="dashboard-body">
            <!-- معلومات المستخدم -->
            <div class="alert alert-success">
                <h4><i class="fas fa-check-circle me-2"></i>تم تسجيل الدخول بنجاح!</h4>
                <div class="row">
                    <div class="col-md-3">
                        <strong>معرف المستخدم:</strong> <?php echo $user_id; ?>
                    </div>
                    <div class="col-md-3">
                        <strong>اسم المستخدم:</strong> <?php echo htmlspecialchars($username); ?>
                    </div>
                    <div class="col-md-3">
                        <strong>الاسم الكامل:</strong> <?php echo htmlspecialchars($full_name); ?>
                    </div>
                    <div class="col-md-3">
                        <strong>الدور:</strong> <?php echo htmlspecialchars($user_role); ?>
                    </div>
                </div>
                <div class="row mt-2">
                    <div class="col-md-3">
                        <strong>المكتب:</strong> <?php echo $office_id; ?>
                    </div>
                    <div class="col-md-3">
                        <strong>وقت الدخول:</strong> <?php echo date('Y-m-d H:i:s'); ?>
                    </div>
                    <div class="col-md-6">
                        <strong>معرف الجلسة:</strong> <?php echo session_id(); ?>
                    </div>
                </div>
            </div>
            
            <!-- إحصائيات سريعة -->
            <div class="row">
                <div class="col-lg-3 col-md-6">
                    <div class="stats-card">
                        <div class="stats-number">0</div>
                        <div class="stats-label">إجمالي الديون</div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6">
                    <div class="stats-card" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);">
                        <div class="stats-number">0</div>
                        <div class="stats-label">الديون المتأخرة</div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6">
                    <div class="stats-card" style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);">
                        <div class="stats-number">0.00 ر.س</div>
                        <div class="stats-label">رصيد الصندوق</div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6">
                    <div class="stats-card" style="background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);">
                        <div class="stats-number">0.00 ر.س</div>
                        <div class="stats-label">الأرصدة البنكية</div>
                    </div>
                </div>
            </div>
            
            <!-- الوحدات المتاحة -->
            <div class="row">
                <div class="col-md-6">
                    <div class="feature-card">
                        <h5><i class="fas fa-money-bill-wave text-primary me-2"></i>إدارة الديون</h5>
                        <p class="mb-3">تتبع ديون العملاء والموردين مع تنبيهات الاستحقاق</p>
                        <a href="modules/debts/" class="btn btn-primary">
                            <i class="fas fa-arrow-left me-2"></i>
                            دخول
                        </a>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="feature-card">
                        <h5><i class="fas fa-address-book text-success me-2"></i>إدارة جهات الاتصال</h5>
                        <p class="mb-3">إدارة بيانات العملاء والموردين</p>
                        <a href="modules/contacts/" class="btn btn-success">
                            <i class="fas fa-arrow-left me-2"></i>
                            دخول
                        </a>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="feature-card">
                        <h5><i class="fas fa-cash-register text-info me-2"></i>إدارة الصندوق</h5>
                        <p class="mb-3">تسجيل جميع المعاملات النقدية والكشوف اليومية</p>
                        <a href="modules/cashbox/" class="btn btn-info">
                            <i class="fas fa-arrow-left me-2"></i>
                            دخول (قريباً)
                        </a>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="feature-card">
                        <h5><i class="fas fa-university text-warning me-2"></i>الحسابات البنكية</h5>
                        <p class="mb-3">متابعة أرصدة البنوك والتحويلات</p>
                        <a href="modules/banks/" class="btn btn-warning">
                            <i class="fas fa-arrow-left me-2"></i>
                            دخول (قريباً)
                        </a>
                    </div>
                </div>
            </div>
            
            <!-- معلومات النظام -->
            <div class="alert alert-info">
                <h5><i class="fas fa-info-circle me-2"></i>معلومات النظام</h5>
                <div class="row">
                    <div class="col-md-4">
                        <strong>إصدار PHP:</strong> <?php echo PHP_VERSION; ?>
                    </div>
                    <div class="col-md-4">
                        <strong>الخادم:</strong> <?php echo $_SERVER['SERVER_SOFTWARE'] ?? 'غير محدد'; ?>
                    </div>
                    <div class="col-md-4">
                        <strong>التاريخ:</strong> <?php echo date('Y-m-d H:i:s'); ?>
                    </div>
                </div>
            </div>
            
            <!-- روابط إدارية -->
            <div class="text-center">
                <h5>🔧 أدوات إدارية</h5>
                <a href="login-debug.php" class="btn btn-outline-primary">تشخيص تسجيل الدخول</a>
                <a href="fix-password.php" class="btn btn-outline-secondary">إصلاح كلمة المرور</a>
                <a href="check-db.php" class="btn btn-outline-info">فحص قاعدة البيانات</a>
                <a href="setup.php" class="btn btn-outline-warning">إعداد النظام</a>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
