<?php
/**
 * الصفحة الرئيسية - لوحة التحكم
 * Dashboard - Main Page
 */

require_once 'config/config.php';
require_once 'config/database.php';

// التحقق من تسجيل الدخول
requireLogin();

$page_title = 'لوحة التحكم';

// إحصائيات سريعة
$database = new Database();

try {
    // إجمالي الديون المعلقة
    $pending_debts = $database->fetch(
        "SELECT COUNT(*) as count, COALESCE(SUM(remaining_amount), 0) as total 
         FROM debts 
         WHERE status IN ('pending', 'partial') 
         AND (office_id = ? OR ? = 'admin')",
        [$_SESSION['office_id'], $_SESSION['user_role']]
    );
    
    // إجمالي الديون المتأخرة
    $overdue_debts = $database->fetch(
        "SELECT COUNT(*) as count, COALESCE(SUM(remaining_amount), 0) as total 
         FROM debts 
         WHERE status IN ('pending', 'partial') 
         AND due_date < CURDATE()
         AND (office_id = ? OR ? = 'admin')",
        [$_SESSION['office_id'], $_SESSION['user_role']]
    );
    
    // رصيد الصندوق
    $cashbox_balance = $database->fetch(
        "SELECT 
            COALESCE(SUM(CASE WHEN type = 'income' THEN amount ELSE 0 END), 0) - 
            COALESCE(SUM(CASE WHEN type = 'expense' THEN amount ELSE 0 END), 0) as balance
         FROM cashbox_transactions 
         WHERE office_id = ? OR ? = 'admin'",
        [$_SESSION['office_id'], $_SESSION['user_role']]
    );
    
    // إجمالي الأرصدة البنكية
    $bank_balance = $database->fetch(
        "SELECT COALESCE(SUM(balance), 0) as total 
         FROM bank_accounts 
         WHERE is_active = 1 
         AND (office_id = ? OR ? = 'admin')",
        [$_SESSION['office_id'], $_SESSION['user_role']]
    );
    
    // آخر المعاملات
    $recent_transactions = $database->fetchAll(
        "SELECT 'cashbox' as source, id, type, amount, description, transaction_date as date, created_at
         FROM cashbox_transactions 
         WHERE office_id = ? OR ? = 'admin'
         UNION ALL
         SELECT 'bank' as source, id, type, amount, description, transaction_date as date, created_at
         FROM bank_transactions bt
         JOIN bank_accounts ba ON bt.bank_account_id = ba.id
         WHERE ba.office_id = ? OR ? = 'admin'
         ORDER BY created_at DESC 
         LIMIT 10",
        [$_SESSION['office_id'], $_SESSION['user_role'], $_SESSION['office_id'], $_SESSION['user_role']]
    );
    
    // الديون المستحقة قريباً (خلال 7 أيام)
    $upcoming_debts = $database->fetchAll(
        "SELECT d.*, c.name as contact_name, c.type as contact_type
         FROM debts d
         JOIN contacts c ON d.contact_id = c.id
         WHERE d.status IN ('pending', 'partial')
         AND d.due_date BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL 7 DAY)
         AND (d.office_id = ? OR ? = 'admin')
         ORDER BY d.due_date ASC
         LIMIT 5",
        [$_SESSION['office_id'], $_SESSION['user_role']]
    );
    
} catch (Exception $e) {
    $error_message = "خطأ في جلب البيانات: " . $e->getMessage();
}

include 'includes/header.php';
?>

<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-tachometer-alt me-2"></i>
        لوحة التحكم
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <button type="button" class="btn btn-sm btn-outline-secondary">
                <i class="fas fa-calendar me-1"></i>
                <?php echo date('Y-m-d'); ?>
            </button>
        </div>
    </div>
</div>

<?php if (isset($error_message)): ?>
    <div class="alert alert-danger">
        <i class="fas fa-exclamation-triangle me-2"></i>
        <?php echo $error_message; ?>
    </div>
<?php endif; ?>

<!-- إحصائيات سريعة -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stats-card">
            <div class="d-flex justify-content-between">
                <div>
                    <div class="stats-number"><?php echo number_format($pending_debts['count'] ?? 0); ?></div>
                    <div class="stats-label">الديون المعلقة</div>
                    <small><?php echo formatCurrency($pending_debts['total'] ?? 0); ?></small>
                </div>
                <div class="align-self-center">
                    <i class="fas fa-money-bill-wave fa-2x opacity-75"></i>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stats-card" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);">
            <div class="d-flex justify-content-between">
                <div>
                    <div class="stats-number"><?php echo number_format($overdue_debts['count'] ?? 0); ?></div>
                    <div class="stats-label">الديون المتأخرة</div>
                    <small><?php echo formatCurrency($overdue_debts['total'] ?? 0); ?></small>
                </div>
                <div class="align-self-center">
                    <i class="fas fa-exclamation-triangle fa-2x opacity-75"></i>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stats-card" style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);">
            <div class="d-flex justify-content-between">
                <div>
                    <div class="stats-number"><?php echo formatCurrency($cashbox_balance['balance'] ?? 0); ?></div>
                    <div class="stats-label">رصيد الصندوق</div>
                </div>
                <div class="align-self-center">
                    <i class="fas fa-cash-register fa-2x opacity-75"></i>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stats-card" style="background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);">
            <div class="d-flex justify-content-between">
                <div>
                    <div class="stats-number"><?php echo formatCurrency($bank_balance['total'] ?? 0); ?></div>
                    <div class="stats-label">الأرصدة البنكية</div>
                </div>
                <div class="align-self-center">
                    <i class="fas fa-university fa-2x opacity-75"></i>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- آخر المعاملات -->
    <div class="col-lg-8 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-history me-2"></i>
                    آخر المعاملات
                </h5>
            </div>
            <div class="card-body">
                <?php if (empty($recent_transactions)): ?>
                    <div class="text-center py-4">
                        <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                        <p class="text-muted">لا توجد معاملات حديثة</p>
                    </div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>المصدر</th>
                                    <th>النوع</th>
                                    <th>المبلغ</th>
                                    <th>الوصف</th>
                                    <th>التاريخ</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($recent_transactions as $transaction): ?>
                                <tr>
                                    <td>
                                        <span class="badge bg-<?php echo $transaction['source'] == 'cashbox' ? 'primary' : 'success'; ?>">
                                            <?php echo $transaction['source'] == 'cashbox' ? 'صندوق' : 'بنك'; ?>
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge bg-<?php echo $transaction['type'] == 'income' || $transaction['type'] == 'deposit' ? 'success' : 'danger'; ?>">
                                            <?php 
                                            $type_labels = [
                                                'income' => 'دخل',
                                                'expense' => 'مصروف',
                                                'deposit' => 'إيداع',
                                                'withdrawal' => 'سحب'
                                            ];
                                            echo $type_labels[$transaction['type']] ?? $transaction['type'];
                                            ?>
                                        </span>
                                    </td>
                                    <td><?php echo formatCurrency($transaction['amount']); ?></td>
                                    <td><?php echo htmlspecialchars(substr($transaction['description'], 0, 50)); ?></td>
                                    <td><?php echo formatDate($transaction['date']); ?></td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <!-- الديون المستحقة قريباً -->
    <div class="col-lg-4 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-clock me-2"></i>
                    ديون مستحقة قريباً
                </h5>
            </div>
            <div class="card-body">
                <?php if (empty($upcoming_debts)): ?>
                    <div class="text-center py-4">
                        <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                        <p class="text-muted">لا توجد ديون مستحقة قريباً</p>
                    </div>
                <?php else: ?>
                    <div class="list-group list-group-flush">
                        <?php foreach ($upcoming_debts as $debt): ?>
                        <div class="list-group-item px-0">
                            <div class="d-flex justify-content-between align-items-start">
                                <div class="ms-2 me-auto">
                                    <div class="fw-bold"><?php echo htmlspecialchars($debt['contact_name']); ?></div>
                                    <small class="text-muted">
                                        <?php echo CONTACT_TYPES[$debt['contact_type']]; ?>
                                    </small>
                                </div>
                                <div class="text-end">
                                    <div class="fw-bold text-danger">
                                        <?php echo formatCurrency($debt['remaining_amount']); ?>
                                    </div>
                                    <small class="text-muted">
                                        <?php echo formatDate($debt['due_date']); ?>
                                    </small>
                                </div>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    </div>
                    <div class="text-center mt-3">
                        <a href="modules/debts/" class="btn btn-sm btn-outline-primary">
                            عرض جميع الديون
                        </a>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- روابط سريعة -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-bolt me-2"></i>
                    إجراءات سريعة
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <?php if (hasPermission('debts_manage')): ?>
                    <div class="col-md-3 mb-3">
                        <a href="modules/debts/add.php" class="btn btn-outline-primary w-100">
                            <i class="fas fa-plus-circle mb-2 d-block fa-2x"></i>
                            إضافة دين جديد
                        </a>
                    </div>
                    <?php endif; ?>
                    
                    <?php if (hasPermission('cashbox_manage')): ?>
                    <div class="col-md-3 mb-3">
                        <a href="modules/cashbox/add.php" class="btn btn-outline-success w-100">
                            <i class="fas fa-cash-register mb-2 d-block fa-2x"></i>
                            إضافة معاملة صندوق
                        </a>
                    </div>
                    <?php endif; ?>
                    
                    <?php if (hasPermission('banks_manage')): ?>
                    <div class="col-md-3 mb-3">
                        <a href="modules/banks/transactions.php" class="btn btn-outline-info w-100">
                            <i class="fas fa-university mb-2 d-block fa-2x"></i>
                            معاملة بنكية
                        </a>
                    </div>
                    <?php endif; ?>
                    
                    <?php if (hasPermission('reports_view')): ?>
                    <div class="col-md-3 mb-3">
                        <a href="modules/reports/" class="btn btn-outline-warning w-100">
                            <i class="fas fa-chart-bar mb-2 d-block fa-2x"></i>
                            عرض التقارير
                        </a>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include 'includes/footer.php'; ?>
