# دليل حل المشاكل - نظام الإدارة المالية
## Troubleshooting Guide - Financial Management System

## 🚨 حل مشكلة Internal Server Error

إذا واجهت خطأ "Internal Server Error"، اتبع هذه الخطوات:

### 1. تشغيل اختبار النظام
```
http://localhost/new2/test.php
```
هذا الملف سيفحص جميع المتطلبات ويخبرك بالمشكلة بالضبط.

### 2. الخطوات السريعة للحل

#### أ) استخدام صفحة الترحيب:
```
http://localhost/new2/welcome.php
```

#### ب) تشغيل معالج الإعداد:
```
http://localhost/new2/setup.php
```

#### ج) تسجيل الدخول المباشر:
```
http://localhost/new2/login.php
```

### 3. المشاكل الشائعة وحلولها

#### مشكلة: "Class 'Database' not found"
**الحل:**
1. تأكد من وجود ملف `config/database.php`
2. إذا لم يكن موجوداً، شغل `setup.php`

#### مشكلة: "Table doesn't exist"
**الحل:**
1. شغل معالج الإعداد: `setup.php`
2. أو استورد قاعدة البيانات يدوياً من `database/schema.sql`

#### مشكلة: "Session already started"
**الحل:**
تم إصلاحها تلقائياً في الكود الجديد.

#### مشكلة: "Permission denied"
**الحل:**
```bash
# على Linux/Mac
chmod 755 config/
chmod 644 config/*.php

# على Windows
تأكد من تشغيل الخادم كمدير
```

### 4. إعداد قاعدة البيانات يدوياً

إذا فشل معالج الإعداد:

```sql
-- 1. إنشاء قاعدة البيانات
CREATE DATABASE financial_management CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 2. استيراد الهيكل
USE financial_management;
SOURCE database/schema.sql;
```

### 5. إعداد ملف database.php يدوياً

إنشئ ملف `config/database.php`:

```php
<?php
class Database {
    private $host = 'localhost';
    private $db_name = 'financial_management';
    private $username = 'root';
    private $password = '';
    private $charset = 'utf8mb4';
    public $conn;

    public function getConnection() {
        $this->conn = null;
        try {
            $dsn = "mysql:host=" . $this->host . ";dbname=" . $this->db_name . ";charset=" . $this->charset;
            $options = [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
            ];
            $this->conn = new PDO($dsn, $this->username, $this->password, $options);
        } catch(PDOException $exception) {
            echo "خطأ في الاتصال: " . $exception->getMessage();
        }
        return $this->conn;
    }
    
    // باقي الدوال...
}

$database = new Database();
$db = $database->getConnection();
?>
```

### 6. تشغيل النظام بدون قاعدة بيانات

للاختبار السريع، يمكنك:
1. فتح `welcome.php` - صفحة ترحيب بسيطة
2. فتح `test.php` - اختبار شامل للنظام

### 7. بيانات الدخول الافتراضية

بعد إكمال الإعداد:
```
اسم المستخدم: admin
كلمة المرور: password
```

### 8. فحص سجلات الأخطاء

#### على XAMPP:
```
xampp/apache/logs/error.log
```

#### على WAMP:
```
wamp/logs/apache_error.log
```

#### على الخادم المحلي:
```
php -S localhost:8000 -t . 2>&1 | tee server.log
```

### 9. إعدادات PHP المطلوبة

تأكد من تفعيل:
- `extension=pdo_mysql`
- `extension=json`
- `session.auto_start = 0`

### 10. خطوات التشخيص المتقدم

1. **فحص إصدار PHP:**
   ```bash
   php --version
   ```

2. **فحص الإضافات:**
   ```bash
   php -m | grep -i pdo
   ```

3. **اختبار الاتصال بقاعدة البيانات:**
   ```bash
   mysql -u root -p
   ```

### 11. الحلول السريعة

#### إذا كان كل شيء معطل:
1. احذف ملف `config/database.php`
2. اذهب إلى `welcome.php`
3. اضغط "إعداد النظام"

#### إذا كانت قاعدة البيانات معطلة:
1. شغل XAMPP/WAMP
2. تأكد من تشغيل MySQL
3. اذهب إلى `setup.php`

#### إذا كان PHP معطل:
1. تأكد من تثبيت PHP
2. أضف PHP إلى PATH
3. أعد تشغيل الخادم

### 12. طلب المساعدة

إذا استمرت المشكلة:
1. شغل `test.php` وأرسل النتائج
2. تحقق من سجل الأخطاء
3. تأكد من إصدار PHP (7.4+)

---

## 🎯 الخطوات المضمونة للتشغيل

1. **افتح:** `http://localhost/new2/welcome.php`
2. **اضغط:** "اختبار النظام"
3. **إذا كان كل شيء أخضر:** اضغط "إعداد النظام"
4. **اتبع الخطوات الأربع**
5. **سجل دخولك**

**مبروك! النظام يعمل الآن 🎉**
