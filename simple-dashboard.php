<?php
/**
 * لوحة تحكم بسيطة - بدون قاعدة بيانات
 */

session_start();

// التحقق من تسجيل الدخول
if (!isset($_SESSION['logged_in']) || $_SESSION['logged_in'] !== true) {
    header('Location: simple-login.php');
    exit();
}

$username = $_SESSION['username'] ?? 'مستخدم';
$login_time = $_SESSION['login_time'] ?? date('Y-m-d H:i:s');

// معالجة تسجيل الخروج
if (isset($_GET['logout'])) {
    session_destroy();
    header('Location: simple-login.php');
    exit();
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة التحكم البسيطة</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: Arial, sans-serif;
            background: #f8f9fa;
            min-height: 100vh;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .header h1 {
            font-size: 1.8rem;
        }
        
        .user-info {
            display: flex;
            align-items: center;
            gap: 20px;
        }
        
        .logout-btn {
            background: rgba(255,255,255,0.2);
            color: white;
            padding: 10px 20px;
            text-decoration: none;
            border-radius: 5px;
            transition: background 0.3s ease;
        }
        
        .logout-btn:hover {
            background: rgba(255,255,255,0.3);
            color: white;
            text-decoration: none;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 30px 20px;
        }
        
        .welcome-card {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            margin-bottom: 30px;
            text-align: center;
        }
        
        .success-message {
            background: #d4edda;
            color: #155724;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            border: 1px solid #c3e6cb;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: white;
            padding: 25px;
            border-radius: 15px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            text-align: center;
            transition: transform 0.3s ease;
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
        }
        
        .stat-card.primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .stat-card.success {
            background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
            color: white;
        }
        
        .stat-card.warning {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
        }
        
        .stat-card.info {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
        }
        
        .stat-number {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .stat-label {
            font-size: 1.1rem;
            opacity: 0.9;
        }
        
        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .feature-card {
            background: white;
            padding: 25px;
            border-radius: 15px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            border-right: 4px solid #667eea;
        }
        
        .feature-card h3 {
            color: #333;
            margin-bottom: 15px;
        }
        
        .feature-card p {
            color: #666;
            margin-bottom: 20px;
            line-height: 1.6;
        }
        
        .feature-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 10px 20px;
            text-decoration: none;
            border-radius: 8px;
            display: inline-block;
            transition: transform 0.3s ease;
        }
        
        .feature-btn:hover {
            transform: translateY(-2px);
            color: white;
            text-decoration: none;
        }
        
        .feature-btn.disabled {
            background: #6c757d;
            cursor: not-allowed;
        }
        
        .info-section {
            background: white;
            padding: 25px;
            border-radius: 15px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }
        
        .info-item {
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
        }
        
        .info-item strong {
            color: #333;
        }
        
        @media (max-width: 768px) {
            .header-content {
                flex-direction: column;
                gap: 15px;
                text-align: center;
            }
            
            .user-info {
                flex-direction: column;
                gap: 10px;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="header-content">
            <h1>🏦 نظام الإدارة المالية</h1>
            <div class="user-info">
                <span>مرحباً، <strong><?php echo htmlspecialchars($username); ?></strong></span>
                <a href="?logout=1" class="logout-btn" onclick="return confirm('هل أنت متأكد من تسجيل الخروج؟')">
                    🚪 تسجيل الخروج
                </a>
            </div>
        </div>
    </div>
    
    <div class="container">
        <div class="welcome-card">
            <div class="success-message">
                <h2>🎉 مبروك! تم تسجيل الدخول بنجاح</h2>
                <p>أهلاً وسهلاً بك في نظام الإدارة المالية البسيط</p>
            </div>
            
            <h1>مرحباً بك في لوحة التحكم</h1>
            <p>هذا نظام تسجيل دخول بسيط يعمل بدون قاعدة بيانات لأغراض الاختبار</p>
        </div>
        
        <!-- إحصائيات وهمية -->
        <div class="stats-grid">
            <div class="stat-card primary">
                <div class="stat-number">15</div>
                <div class="stat-label">إجمالي الديون</div>
            </div>
            
            <div class="stat-card warning">
                <div class="stat-number">3</div>
                <div class="stat-label">ديون متأخرة</div>
            </div>
            
            <div class="stat-card info">
                <div class="stat-number">25,000</div>
                <div class="stat-label">رصيد الصندوق</div>
            </div>
            
            <div class="stat-card success">
                <div class="stat-number">150,000</div>
                <div class="stat-label">الأرصدة البنكية</div>
            </div>
        </div>
        
        <!-- الوحدات -->
        <div class="features-grid">
            <div class="feature-card">
                <h3>💰 إدارة الديون</h3>
                <p>تتبع ديون العملاء والموردين مع تنبيهات الاستحقاق والمتابعة الدورية</p>
                <a href="modules/debts/" class="feature-btn">دخول للوحدة</a>
            </div>
            
            <div class="feature-card">
                <h3>👥 إدارة جهات الاتصال</h3>
                <p>إدارة بيانات العملاء والموردين وتنظيم معلومات الاتصال</p>
                <a href="modules/contacts/" class="feature-btn">دخول للوحدة</a>
            </div>
            
            <div class="feature-card">
                <h3>💵 إدارة الصندوق</h3>
                <p>تسجيل جميع المعاملات النقدية والكشوف اليومية والشهرية</p>
                <a href="#" class="feature-btn disabled">قريباً</a>
            </div>
            
            <div class="feature-card">
                <h3>🏛️ الحسابات البنكية</h3>
                <p>متابعة أرصدة البنوك والتحويلات والمعاملات البنكية</p>
                <a href="#" class="feature-btn disabled">قريباً</a>
            </div>
            
            <div class="feature-card">
                <h3>📊 التقارير</h3>
                <p>تقارير مالية شاملة وتحليلات مفصلة للأداء المالي</p>
                <a href="#" class="feature-btn disabled">قريباً</a>
            </div>
            
            <div class="feature-card">
                <h3>⚙️ إدارة النظام</h3>
                <p>إعدادات النظام وإدارة المستخدمين والصلاحيات</p>
                <a href="login-debug.php" class="feature-btn">أدوات التشخيص</a>
            </div>
        </div>
        
        <!-- معلومات الجلسة -->
        <div class="info-section">
            <h3>📋 معلومات الجلسة</h3>
            <div class="info-grid">
                <div class="info-item">
                    <strong>اسم المستخدم:</strong><br>
                    <?php echo htmlspecialchars($username); ?>
                </div>
                <div class="info-item">
                    <strong>وقت تسجيل الدخول:</strong><br>
                    <?php echo $login_time; ?>
                </div>
                <div class="info-item">
                    <strong>معرف الجلسة:</strong><br>
                    <?php echo substr(session_id(), 0, 10) . '...'; ?>
                </div>
                <div class="info-item">
                    <strong>عنوان IP:</strong><br>
                    <?php echo $_SERVER['REMOTE_ADDR'] ?? 'غير محدد'; ?>
                </div>
                <div class="info-item">
                    <strong>المتصفح:</strong><br>
                    <?php echo substr($_SERVER['HTTP_USER_AGENT'] ?? 'غير محدد', 0, 30) . '...'; ?>
                </div>
                <div class="info-item">
                    <strong>إصدار PHP:</strong><br>
                    <?php echo PHP_VERSION; ?>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
