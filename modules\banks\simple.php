<?php
/**
 * وحدة إدارة الحسابات البنكية
 */

session_start();

// التحقق من تسجيل الدخول
if (!isset($_SESSION['logged_in']) || $_SESSION['logged_in'] !== true) {
    header('Location: ../../simple-login.php');
    exit();
}

// إعدادات العملة
$currency = 'ر.س';

// بيانات وهمية للحسابات البنكية
$bank_accounts = [
    [
        'id' => 1,
        'bank_name' => 'البنك الأهلي السعودي',
        'account_number' => '**********',
        'account_name' => 'الحساب الجاري الرئيسي',
        'balance' => 150000,
        'currency' => 'SAR',
        'status' => 'نشط',
        'last_transaction' => '2024-01-15'
    ],
    [
        'id' => 2,
        'bank_name' => 'بنك الراجحي',
        'account_number' => '**********',
        'account_name' => 'حساب التوفير',
        'balance' => 85000,
        'currency' => 'SAR',
        'status' => 'نشط',
        'last_transaction' => '2024-01-14'
    ],
    [
        'id' => 3,
        'bank_name' => 'بنك الرياض',
        'account_number' => '**********',
        'account_name' => 'حساب العمليات',
        'balance' => 45000,
        'currency' => 'SAR',
        'status' => 'نشط',
        'last_transaction' => '2024-01-13'
    ],
    [
        'id' => 4,
        'bank_name' => 'البنك السعودي للاستثمار',
        'account_number' => '**********',
        'account_name' => 'حساب الاستثمار',
        'balance' => 200000,
        'currency' => 'SAR',
        'status' => 'مجمد',
        'last_transaction' => '2024-01-10'
    ]
];

// بيانات وهمية للمعاملات البنكية
$bank_transactions = [
    [
        'id' => 1,
        'account_id' => 1,
        'date' => '2024-01-15',
        'type' => 'deposit',
        'amount' => 25000,
        'description' => 'إيداع من العميل أحمد محمد',
        'reference' => 'DEP-001',
        'balance_after' => 150000
    ],
    [
        'id' => 2,
        'account_id' => 1,
        'date' => '2024-01-15',
        'type' => 'withdrawal',
        'amount' => 5000,
        'description' => 'سحب نقدي',
        'reference' => 'WTH-001',
        'balance_after' => 145000
    ],
    [
        'id' => 3,
        'account_id' => 2,
        'date' => '2024-01-14',
        'type' => 'transfer_in',
        'amount' => 15000,
        'description' => 'تحويل من الحساب الجاري',
        'reference' => 'TRF-001',
        'balance_after' => 85000
    ],
    [
        'id' => 4,
        'account_id' => 3,
        'date' => '2024-01-13',
        'type' => 'deposit',
        'amount' => 10000,
        'description' => 'إيداع شيك',
        'reference' => 'CHK-001',
        'balance_after' => 45000
    ]
];

// حساب الإحصائيات
$total_balance = array_sum(array_column($bank_accounts, 'balance'));
$active_accounts = count(array_filter($bank_accounts, function($acc) { return $acc['status'] === 'نشط'; }));
$total_transactions = count($bank_transactions);

// دالة لتحديد لون حالة الحساب
function getAccountStatusColor($status) {
    return $status === 'نشط' ? 'success' : 'warning';
}

// دالة لتحديد لون نوع المعاملة
function getTransactionColor($type) {
    switch ($type) {
        case 'deposit': return 'success';
        case 'withdrawal': return 'danger';
        case 'transfer_in': return 'info';
        case 'transfer_out': return 'warning';
        default: return 'secondary';
    }
}

// دالة لتحديد نص نوع المعاملة
function getTransactionText($type) {
    switch ($type) {
        case 'deposit': return 'إيداع';
        case 'withdrawal': return 'سحب';
        case 'transfer_in': return 'تحويل وارد';
        case 'transfer_out': return 'تحويل صادر';
        default: return 'أخرى';
    }
}

// دالة لتحديد أيقونة نوع المعاملة
function getTransactionIcon($type) {
    switch ($type) {
        case 'deposit': return 'fas fa-arrow-down';
        case 'withdrawal': return 'fas fa-arrow-up';
        case 'transfer_in': return 'fas fa-arrow-right';
        case 'transfer_out': return 'fas fa-arrow-left';
        default: return 'fas fa-exchange-alt';
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الحسابات البنكية - نظام الإدارة المالية</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .header {
            background: linear-gradient(135deg, #6f42c1 0%, #5a2d91 100%);
            color: white;
            padding: 20px 0;
            margin-bottom: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .stats-card {
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            text-align: center;
            transition: transform 0.3s ease;
        }
        
        .stats-card:hover {
            transform: translateY(-5px);
        }
        
        .stats-number {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .account-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            transition: transform 0.3s ease;
            border-right: 5px solid #6f42c1;
        }
        
        .account-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 15px rgba(0,0,0,0.15);
        }
        
        .account-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .account-balance {
            font-size: 1.8rem;
            font-weight: bold;
            color: #28a745;
        }
        
        .transaction-item {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 10px;
            border-left: 4px solid #6f42c1;
        }
        
        .breadcrumb {
            background: white;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="container">
            <div class="d-flex justify-content-between align-items-center">
                <h1><i class="fas fa-university me-3"></i>إدارة الحسابات البنكية</h1>
                <div>
                    <a href="../../simple-dashboard.php" class="btn btn-light me-2">
                        <i class="fas fa-home me-1"></i>الرئيسية
                    </a>
                    <a href="../../simple-login.php?logout=1" class="btn btn-outline-light">
                        <i class="fas fa-sign-out-alt me-1"></i>خروج
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <div class="container">
        <!-- Breadcrumb -->
        <nav class="breadcrumb">
            <ol class="breadcrumb mb-0">
                <li class="breadcrumb-item"><a href="../../simple-dashboard.php">الرئيسية</a></li>
                <li class="breadcrumb-item active">إدارة الحسابات البنكية</li>
            </ol>
        </nav>
        
        <!-- إحصائيات سريعة -->
        <div class="row mb-4">
            <div class="col-lg-3 col-md-6">
                <div class="stats-card">
                    <div class="stats-number text-primary"><?php echo count($bank_accounts); ?></div>
                    <div>إجمالي الحسابات</div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="stats-card">
                    <div class="stats-number text-success"><?php echo $active_accounts; ?></div>
                    <div>الحسابات النشطة</div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="stats-card">
                    <div class="stats-number text-info"><?php echo number_format($total_balance); ?> <?php echo $currency; ?></div>
                    <div>إجمالي الأرصدة</div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="stats-card">
                    <div class="stats-number text-warning"><?php echo $total_transactions; ?></div>
                    <div>إجمالي المعاملات</div>
                </div>
            </div>
        </div>
        
        <!-- إجراءات سريعة -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h3><i class="fas fa-list me-2"></i>الحسابات البنكية</h3>
            <div>
                <button class="btn btn-success" onclick="addAccount()">
                    <i class="fas fa-plus me-1"></i>إضافة حساب
                </button>
                <button class="btn btn-info" onclick="bankStatement()">
                    <i class="fas fa-file-alt me-1"></i>كشف حساب
                </button>
                <button class="btn btn-secondary" onclick="reconciliation()">
                    <i class="fas fa-balance-scale me-1"></i>مطابقة
                </button>
            </div>
        </div>
        
        <!-- قائمة الحسابات البنكية -->
        <div class="row">
            <?php foreach ($bank_accounts as $account): ?>
            <div class="col-lg-6 col-md-12">
                <div class="account-card">
                    <div class="account-header">
                        <div>
                            <h5 class="mb-1"><?php echo htmlspecialchars($account['bank_name']); ?></h5>
                            <p class="text-muted mb-0"><?php echo htmlspecialchars($account['account_name']); ?></p>
                        </div>
                        <span class="badge bg-<?php echo getAccountStatusColor($account['status']); ?>">
                            <?php echo $account['status']; ?>
                        </span>
                    </div>
                    
                    <div class="row mb-3">
                        <div class="col-6">
                            <small class="text-muted">رقم الحساب:</small><br>
                            <strong><?php echo htmlspecialchars($account['account_number']); ?></strong>
                        </div>
                        <div class="col-6">
                            <small class="text-muted">آخر معاملة:</small><br>
                            <strong><?php echo $account['last_transaction']; ?></strong>
                        </div>
                    </div>
                    
                    <div class="text-center mb-3">
                        <div class="account-balance">
                            <?php echo number_format($account['balance']); ?> <?php echo $currency; ?>
                        </div>
                        <small class="text-muted">الرصيد الحالي</small>
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <div class="btn-group btn-group-sm">
                            <button class="btn btn-outline-info" onclick="viewAccount(<?php echo $account['id']; ?>)">
                                <i class="fas fa-eye"></i> عرض
                            </button>
                            <button class="btn btn-outline-primary" onclick="editAccount(<?php echo $account['id']; ?>)">
                                <i class="fas fa-edit"></i> تعديل
                            </button>
                            <button class="btn btn-outline-success" onclick="addTransaction(<?php echo $account['id']; ?>)">
                                <i class="fas fa-plus"></i> معاملة
                            </button>
                        </div>
                        
                        <div class="btn-group btn-group-sm">
                            <button class="btn btn-outline-warning" onclick="transfer(<?php echo $account['id']; ?>)">
                                <i class="fas fa-exchange-alt"></i> تحويل
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            <?php endforeach; ?>
        </div>
        
        <!-- آخر المعاملات البنكية -->
        <div class="mt-5">
            <h3><i class="fas fa-history me-2"></i>آخر المعاملات البنكية</h3>
            <div class="row">
                <?php foreach (array_reverse($bank_transactions) as $transaction): ?>
                <?php 
                $account = array_filter($bank_accounts, function($acc) use ($transaction) { 
                    return $acc['id'] == $transaction['account_id']; 
                });
                $account = reset($account);
                ?>
                <div class="col-lg-6 col-md-12">
                    <div class="transaction-item">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <div class="d-flex align-items-center">
                                <i class="<?php echo getTransactionIcon($transaction['type']); ?> text-<?php echo getTransactionColor($transaction['type']); ?> me-2"></i>
                                <span class="badge bg-<?php echo getTransactionColor($transaction['type']); ?>">
                                    <?php echo getTransactionText($transaction['type']); ?>
                                </span>
                            </div>
                            <strong class="text-<?php echo getTransactionColor($transaction['type']); ?>">
                                <?php echo number_format($transaction['amount']); ?> <?php echo $currency; ?>
                            </strong>
                        </div>
                        
                        <div class="mb-2">
                            <small class="text-muted">البنك:</small>
                            <strong><?php echo htmlspecialchars($account['bank_name']); ?></strong>
                        </div>
                        
                        <div class="mb-2">
                            <small class="text-muted">الوصف:</small>
                            <?php echo htmlspecialchars($transaction['description']); ?>
                        </div>
                        
                        <div class="d-flex justify-content-between">
                            <small class="text-muted">
                                <i class="fas fa-calendar me-1"></i><?php echo $transaction['date']; ?>
                            </small>
                            <small class="text-muted">
                                <i class="fas fa-hashtag me-1"></i><?php echo $transaction['reference']; ?>
                            </small>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
        </div>
        
        <!-- معلومات إضافية -->
        <div class="alert alert-success mt-4">
            <h5><i class="fas fa-check-circle me-2"></i>تم تحميل وحدة الحسابات البنكية بنجاح!</h5>
            <ul class="mb-0">
                <li>✅ إجمالي الأرصدة: <strong><?php echo number_format($total_balance); ?> <?php echo $currency; ?></strong></li>
                <li>✅ عدد الحسابات النشطة: <strong><?php echo $active_accounts; ?> من <?php echo count($bank_accounts); ?></strong></li>
                <li>✅ آخر معاملة: <strong><?php echo $bank_transactions[0]['date']; ?></strong></li>
                <li>✅ جميع الوظائف تفاعلية ومتاحة للاختبار</li>
            </ul>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function addAccount() {
            alert('إضافة حساب بنكي جديد\n\nسيتم فتح نموذج لإدخال:\n- اسم البنك\n- رقم الحساب\n- اسم الحساب\n- الرصيد الافتتاحي\n\nهذه وظيفة تجريبية.');
        }
        
        function viewAccount(id) {
            alert('عرض تفاصيل الحساب رقم: ' + id + '\n\nسيتم عرض:\n- تفاصيل الحساب\n- كشف الحساب\n- آخر المعاملات\n\nهذه وظيفة تجريبية.');
        }
        
        function editAccount(id) {
            alert('تعديل الحساب رقم: ' + id + '\n\nيمكن تعديل:\n- اسم الحساب\n- حالة الحساب\n- معلومات إضافية\n\nهذه وظيفة تجريبية.');
        }
        
        function addTransaction(id) {
            const type = prompt('نوع المعاملة:\n1 - إيداع\n2 - سحب\n3 - تحويل\n\nأدخل الرقم:');
            const amount = prompt('أدخل المبلغ:');
            const description = prompt('أدخل وصف المعاملة:');
            
            if (type && amount && description) {
                alert('تم إضافة معاملة للحساب رقم: ' + id + '\nالمبلغ: ' + amount + ' <?php echo $currency; ?>\nالوصف: ' + description + '\n\nهذه وظيفة تجريبية.');
            }
        }
        
        function transfer(id) {
            const targetAccount = prompt('رقم الحساب المستهدف:');
            const amount = prompt('مبلغ التحويل:');
            const description = prompt('وصف التحويل:');
            
            if (targetAccount && amount && description) {
                alert('تحويل من الحساب رقم: ' + id + '\nإلى الحساب: ' + targetAccount + '\nالمبلغ: ' + amount + ' <?php echo $currency; ?>\n\nهذه وظيفة تجريبية.');
            }
        }
        
        function bankStatement() {
            alert('كشف الحساب البنكي\n\nسيتم إنشاء كشف حساب شامل يحتوي على:\n- جميع المعاملات\n- الأرصدة\n- التواريخ\n\nهذه وظيفة تجريبية.');
        }
        
        function reconciliation() {
            alert('مطابقة الحسابات البنكية\n\nسيتم مطابقة:\n- أرصدة النظام مع البنك\n- المعاملات المعلقة\n- الفروقات\n\nهذه وظيفة تجريبية.');
        }
    </script>
</body>
</html>
