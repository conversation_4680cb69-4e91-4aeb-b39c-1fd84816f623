#!/bin/bash

echo "========================================"
echo "    نظام الإدارة المالية الشامل"
echo "    Financial Management System"
echo "========================================"
echo

# التحقق من وجود PHP
if ! command -v php &> /dev/null; then
    echo "خطأ: PHP غير مثبت أو غير موجود في PATH"
    echo "يرجى تثبيت PHP أولاً"
    exit 1
fi

echo "تم العثور على PHP..."
php --version

echo
echo "بدء تشغيل الخادم المحلي..."
echo

# التحقق من وجود ملف الإعداد
if [ ! -f "config/database.php" ]; then
    echo "لم يتم العثور على ملف الإعداد"
    echo "سيتم فتح معالج الإعداد..."
    echo
    
    # فتح المتصفح (حسب النظام)
    if command -v xdg-open &> /dev/null; then
        xdg-open http://localhost:8000/setup.php &
    elif command -v open &> /dev/null; then
        open http://localhost:8000/setup.php &
    else
        echo "يرجى فتح المتصفح والذهاب إلى: http://localhost:8000/setup.php"
    fi
else
    echo "تم العثور على ملف الإعداد"
    echo "سيتم فتح النظام..."
    echo
    
    # فتح المتصفح (حسب النظام)
    if command -v xdg-open &> /dev/null; then
        xdg-open http://localhost:8000/ &
    elif command -v open &> /dev/null; then
        open http://localhost:8000/ &
    else
        echo "يرجى فتح المتصفح والذهاب إلى: http://localhost:8000/"
    fi
fi

echo "========================================"
echo "الخادم يعمل على: http://localhost:8000"
echo "اضغط Ctrl+C لإيقاف الخادم"
echo "========================================"
echo

# بدء الخادم المحلي
php -S localhost:8000 -t .
