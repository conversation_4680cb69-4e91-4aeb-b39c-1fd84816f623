<?php
/**
 * اختبار تسجيل الدخول
 */

require_once 'config/config.php';
require_once 'config/database.php';

echo "<h1>اختبار تسجيل الدخول</h1>";

$username = $_POST['username'] ?? 'admin';
$password = $_POST['password'] ?? 'password';

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    echo "<h2>نتائج الاختبار:</h2>";
    
    try {
        $database = new Database();
        
        // البحث عن المستخدم
        $sql = "SELECT * FROM users WHERE username = ? AND is_active = 1";
        $user = $database->fetch($sql, [$username]);
        
        if ($user) {
            echo "✅ تم العثور على المستخدم: " . htmlspecialchars($user['full_name']) . "<br>";
            echo "📧 البريد الإلكتروني: " . htmlspecialchars($user['email']) . "<br>";
            echo "👤 الدور: " . htmlspecialchars($user['role']) . "<br>";
            echo "🏢 المكتب: " . htmlspecialchars($user['office_id']) . "<br>";
            
            // اختبار كلمة المرور
            echo "<h3>اختبار كلمة المرور:</h3>";
            echo "كلمة المرور المدخلة: <strong>" . htmlspecialchars($password) . "</strong><br>";
            echo "كلمة المرور المشفرة في قاعدة البيانات: <br><small>" . htmlspecialchars($user['password']) . "</small><br>";
            
            if (password_verify($password, $user['password'])) {
                echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; color: #155724; margin: 10px 0;'>";
                echo "✅ <strong>كلمة المرور صحيحة!</strong><br>";
                echo "يمكنك تسجيل الدخول بهذه البيانات.";
                echo "</div>";
                
                echo "<a href='login.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>تسجيل الدخول الآن</a>";
                
            } else {
                echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; color: #721c24; margin: 10px 0;'>";
                echo "❌ <strong>كلمة المرور خاطئة!</strong><br>";
                echo "جرب إعادة تعيين كلمة المرور.";
                echo "</div>";
                
                echo "<a href='reset-password.php' style='background: #dc3545; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>إعادة تعيين كلمة المرور</a>";
            }
            
        } else {
            echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; color: #721c24; margin: 10px 0;'>";
            echo "❌ <strong>المستخدم غير موجود!</strong><br>";
            echo "تأكد من اسم المستخدم أو أنشئ مستخدم جديد.";
            echo "</div>";
        }
        
    } catch (Exception $e) {
        echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; color: #721c24; margin: 10px 0;'>";
        echo "❌ <strong>خطأ:</strong> " . htmlspecialchars($e->getMessage());
        echo "</div>";
    }
    
    echo "<hr>";
}

// عرض جميع المستخدمين
try {
    $database = new Database();
    $users = $database->fetchAll("SELECT username, full_name, email, role, is_active FROM users ORDER BY username");
    
    if (!empty($users)) {
        echo "<h2>المستخدمون الموجودون:</h2>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
        echo "<tr style='background: #f8f9fa;'>";
        echo "<th style='padding: 10px;'>اسم المستخدم</th>";
        echo "<th style='padding: 10px;'>الاسم الكامل</th>";
        echo "<th style='padding: 10px;'>البريد الإلكتروني</th>";
        echo "<th style='padding: 10px;'>الدور</th>";
        echo "<th style='padding: 10px;'>نشط</th>";
        echo "</tr>";
        
        foreach ($users as $user) {
            echo "<tr>";
            echo "<td style='padding: 10px;'><strong>" . htmlspecialchars($user['username']) . "</strong></td>";
            echo "<td style='padding: 10px;'>" . htmlspecialchars($user['full_name']) . "</td>";
            echo "<td style='padding: 10px;'>" . htmlspecialchars($user['email']) . "</td>";
            echo "<td style='padding: 10px;'>" . htmlspecialchars($user['role']) . "</td>";
            echo "<td style='padding: 10px;'>" . ($user['is_active'] ? '✅' : '❌') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
} catch (Exception $e) {
    echo "خطأ في جلب المستخدمين: " . htmlspecialchars($e->getMessage());
}
?>

<hr>

<h2>اختبار تسجيل الدخول:</h2>
<form method="POST" style="background: #f8f9fa; padding: 20px; border-radius: 5px;">
    <div style="margin-bottom: 15px;">
        <label for="username">اسم المستخدم:</label><br>
        <input type="text" id="username" name="username" value="<?php echo htmlspecialchars($username); ?>" 
               style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
    </div>
    
    <div style="margin-bottom: 15px;">
        <label for="password">كلمة المرور:</label><br>
        <input type="text" id="password" name="password" value="<?php echo htmlspecialchars($password); ?>" 
               style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
        <small style="color: #666;">كلمة المرور الافتراضية: password</small>
    </div>
    
    <button type="submit" style="background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer;">
        اختبار تسجيل الدخول
    </button>
</form>

<hr>

<h3>روابط مفيدة:</h3>
<a href="reset-password.php" style="background: #dc3545; color: white; padding: 8px 16px; text-decoration: none; border-radius: 4px; margin: 5px;">إعادة تعيين كلمة المرور</a>
<a href="login.php" style="background: #28a745; color: white; padding: 8px 16px; text-decoration: none; border-radius: 4px; margin: 5px;">صفحة تسجيل الدخول</a>
<a href="setup.php" style="background: #6c757d; color: white; padding: 8px 16px; text-decoration: none; border-radius: 4px; margin: 5px;">إعداد النظام</a>
