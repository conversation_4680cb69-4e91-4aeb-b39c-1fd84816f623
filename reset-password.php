<?php
/**
 * إعادة تعيين كلمة المرور للمدير
 */

require_once 'config/config.php';
require_once 'config/database.php';

$message = '';
$success = false;

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $username = $_POST['username'] ?? 'admin';
    $new_password = $_POST['password'] ?? 'password';
    
    try {
        $database = new Database();
        
        // تشفير كلمة المرور الجديدة
        $hashed_password = password_hash($new_password, PASSWORD_DEFAULT);
        
        // تحديث كلمة المرور
        $update_data = [
            'password' => $hashed_password
        ];
        
        $result = $database->update('users', $update_data, 'username = ?', [$username]);
        
        if ($result) {
            $message = "تم تحديث كلمة المرور بنجاح!";
            $success = true;
            
            // تسجيل النشاط
            logActivity('إعادة تعيين كلمة المرور', 'users', null);
        } else {
            $message = "فشل في تحديث كلمة المرور";
        }
        
    } catch (Exception $e) {
        $message = "خطأ: " . $e->getMessage();
    }
}

// جلب قائمة المستخدمين
try {
    $database = new Database();
    $users = $database->fetchAll("SELECT id, username, full_name, role FROM users ORDER BY username");
} catch (Exception $e) {
    $users = [];
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعادة تعيين كلمة المرور</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .container {
            max-width: 600px;
            margin: 50px auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .body {
            padding: 30px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h2><i class="fas fa-key me-2"></i>إعادة تعيين كلمة المرور</h2>
            <p class="mb-0">إعادة تعيين كلمة مرور المستخدمين</p>
        </div>
        
        <div class="body">
            <?php if ($message): ?>
                <div class="alert alert-<?php echo $success ? 'success' : 'danger'; ?>">
                    <?php echo $message; ?>
                </div>
            <?php endif; ?>
            
            <?php if (!empty($users)): ?>
                <h4>المستخدمون الموجودون:</h4>
                <div class="table-responsive mb-4">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>اسم المستخدم</th>
                                <th>الاسم الكامل</th>
                                <th>الدور</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($users as $user): ?>
                            <tr>
                                <td><strong><?php echo htmlspecialchars($user['username']); ?></strong></td>
                                <td><?php echo htmlspecialchars($user['full_name']); ?></td>
                                <td>
                                    <span class="badge bg-<?php echo $user['role'] == 'admin' ? 'danger' : 'primary'; ?>">
                                        <?php echo USER_ROLES[$user['role']] ?? $user['role']; ?>
                                    </span>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php endif; ?>
            
            <form method="POST">
                <div class="mb-3">
                    <label for="username" class="form-label">اسم المستخدم</label>
                    <input type="text" class="form-control" id="username" name="username" 
                           value="<?php echo htmlspecialchars($_POST['username'] ?? 'admin'); ?>" required>
                </div>
                
                <div class="mb-3">
                    <label for="password" class="form-label">كلمة المرور الجديدة</label>
                    <input type="text" class="form-control" id="password" name="password" 
                           value="<?php echo htmlspecialchars($_POST['password'] ?? 'password'); ?>" required>
                    <div class="form-text">كلمة المرور الافتراضية: password</div>
                </div>
                
                <button type="submit" class="btn btn-primary w-100">
                    <i class="fas fa-save me-2"></i>
                    تحديث كلمة المرور
                </button>
            </form>
            
            <?php if ($success): ?>
                <div class="mt-4 text-center">
                    <a href="login.php" class="btn btn-success">
                        <i class="fas fa-sign-in-alt me-2"></i>
                        تسجيل الدخول الآن
                    </a>
                </div>
            <?php endif; ?>
            
            <hr class="my-4">
            
            <div class="alert alert-info">
                <h6><i class="fas fa-info-circle me-2"></i>معلومات مهمة:</h6>
                <ul class="mb-0">
                    <li>كلمة المرور الافتراضية: <strong>password</strong></li>
                    <li>اسم المستخدم الافتراضي: <strong>admin</strong></li>
                    <li>يمكنك تغيير كلمة المرور لأي مستخدم</li>
                    <li>كلمة المرور ستكون مشفرة تلقائياً</li>
                </ul>
            </div>
            
            <div class="text-center">
                <a href="login.php" class="btn btn-outline-primary me-2">
                    <i class="fas fa-sign-in-alt me-1"></i>
                    تسجيل الدخول
                </a>
                <a href="setup.php" class="btn btn-outline-secondary">
                    <i class="fas fa-cog me-1"></i>
                    إعداد النظام
                </a>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/js/all.min.js"></script>
</body>
</html>
