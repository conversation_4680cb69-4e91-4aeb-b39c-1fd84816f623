<?php
/**
 * وحدة إدارة جهات الاتصال (العملاء والموردين)
 * Contacts Management Module
 */

require_once '../../config/config.php';
require_once '../../config/database.php';

// التحقق من الصلاحيات
requirePermission('debts_view');

$page_title = 'إدارة جهات الاتصال';

// معاملات البحث والتصفية
$search = sanitize($_GET['search'] ?? '');
$type_filter = sanitize($_GET['type'] ?? '');
$office_filter = sanitize($_GET['office'] ?? '');
$page = max(1, intval($_GET['page'] ?? 1));
$limit = RECORDS_PER_PAGE;
$offset = ($page - 1) * $limit;

$database = new Database();

try {
    // بناء استعلام البحث
    $where_conditions = [];
    $params = [];
    
    // تصفية حسب المكتب (إذا لم يكن مدير نظام)
    if ($_SESSION['user_role'] !== 'admin') {
        $where_conditions[] = "c.office_id = ?";
        $params[] = $_SESSION['office_id'];
    } elseif (!empty($office_filter)) {
        $where_conditions[] = "c.office_id = ?";
        $params[] = $office_filter;
    }
    
    // البحث في الاسم أو الهاتف أو البريد الإلكتروني
    if (!empty($search)) {
        $where_conditions[] = "(c.name LIKE ? OR c.phone LIKE ? OR c.email LIKE ?)";
        $params[] = "%$search%";
        $params[] = "%$search%";
        $params[] = "%$search%";
    }
    
    // تصفية حسب النوع
    if (!empty($type_filter)) {
        $where_conditions[] = "c.type = ?";
        $params[] = $type_filter;
    }
    
    // إضافة شرط النشاط
    $where_conditions[] = "c.is_active = 1";
    
    $where_clause = 'WHERE ' . implode(' AND ', $where_conditions);
    
    // استعلام العد الإجمالي
    $count_sql = "SELECT COUNT(*) as total 
                  FROM contacts c 
                  LEFT JOIN offices o ON c.office_id = o.id 
                  $where_clause";
    
    $total_records = $database->fetch($count_sql, $params)['total'];
    $total_pages = ceil($total_records / $limit);
    
    // استعلام البيانات
    $sql = "SELECT c.*, o.name as office_name,
                   (SELECT COUNT(*) FROM debts WHERE contact_id = c.id) as debts_count,
                   (SELECT COALESCE(SUM(remaining_amount), 0) FROM debts WHERE contact_id = c.id AND status IN ('pending', 'partial')) as outstanding_amount
            FROM contacts c 
            LEFT JOIN offices o ON c.office_id = o.id 
            $where_clause 
            ORDER BY c.name ASC 
            LIMIT $limit OFFSET $offset";
    
    $contacts = $database->fetchAll($sql, $params);
    
    // إحصائيات سريعة
    $stats_sql = "SELECT 
                    COUNT(*) as total_count,
                    SUM(CASE WHEN type = 'customer' THEN 1 ELSE 0 END) as customers_count,
                    SUM(CASE WHEN type = 'supplier' THEN 1 ELSE 0 END) as suppliers_count
                  FROM contacts c 
                  $where_clause";
    
    $stats = $database->fetch($stats_sql, $params);
    
    // قائمة المكاتب للتصفية (للمدير فقط)
    $offices = [];
    if ($_SESSION['user_role'] === 'admin') {
        $offices = $database->fetchAll("SELECT id, name FROM offices WHERE is_active = 1 ORDER BY name");
    }
    
} catch (Exception $e) {
    $error_message = "خطأ في جلب البيانات: " . $e->getMessage();
}

include '../../includes/header.php';
?>

<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-address-book me-2"></i>
        إدارة جهات الاتصال
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <?php if (hasPermission('debts_manage')): ?>
        <div class="btn-group me-2">
            <a href="add.php" class="btn btn-primary">
                <i class="fas fa-plus me-1"></i>
                إضافة جهة اتصال
            </a>
        </div>
        <?php endif; ?>
        <div class="btn-group">
            <button type="button" class="btn btn-outline-secondary" onclick="printPage()">
                <i class="fas fa-print me-1"></i>
                طباعة
            </button>
            <button type="button" class="btn btn-outline-secondary" onclick="exportTableToCSV('contacts-table', 'contacts.csv')">
                <i class="fas fa-download me-1"></i>
                تصدير
            </button>
        </div>
    </div>
</div>

<?php if (isset($error_message)): ?>
    <div class="alert alert-danger">
        <i class="fas fa-exclamation-triangle me-2"></i>
        <?php echo $error_message; ?>
    </div>
<?php endif; ?>

<!-- إحصائيات سريعة -->
<div class="row mb-4">
    <div class="col-lg-4 col-md-6 mb-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0"><?php echo number_format($stats['total_count'] ?? 0); ?></h4>
                        <p class="mb-0">إجمالي جهات الاتصال</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-address-book fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4 col-md-6 mb-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0"><?php echo number_format($stats['customers_count'] ?? 0); ?></h4>
                        <p class="mb-0">العملاء</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-users fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4 col-md-6 mb-3">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0"><?php echo number_format($stats['suppliers_count'] ?? 0); ?></h4>
                        <p class="mb-0">الموردين</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-truck fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- فلاتر البحث -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" class="row g-3">
            <div class="col-md-4">
                <label for="search" class="form-label">البحث</label>
                <input type="text" class="form-control" id="search" name="search" 
                       value="<?php echo htmlspecialchars($search); ?>" 
                       placeholder="الاسم، الهاتف، أو البريد الإلكتروني">
            </div>
            
            <div class="col-md-3">
                <label for="type" class="form-label">النوع</label>
                <select class="form-select" id="type" name="type">
                    <option value="">جميع الأنواع</option>
                    <?php foreach (CONTACT_TYPES as $key => $label): ?>
                        <option value="<?php echo $key; ?>" <?php echo $type_filter == $key ? 'selected' : ''; ?>>
                            <?php echo $label; ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>
            
            <?php if ($_SESSION['user_role'] === 'admin'): ?>
            <div class="col-md-3">
                <label for="office" class="form-label">المكتب</label>
                <select class="form-select" id="office" name="office">
                    <option value="">جميع المكاتب</option>
                    <?php foreach ($offices as $office): ?>
                        <option value="<?php echo $office['id']; ?>" <?php echo $office_filter == $office['id'] ? 'selected' : ''; ?>>
                            <?php echo htmlspecialchars($office['name']); ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>
            <?php endif; ?>
            
            <div class="col-md-2">
                <label class="form-label">&nbsp;</label>
                <div class="d-flex gap-2">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search me-1"></i>
                        بحث
                    </button>
                    <a href="index.php" class="btn btn-outline-secondary">
                        <i class="fas fa-times me-1"></i>
                        إلغاء
                    </a>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- جدول جهات الاتصال -->
<div class="card">
    <div class="card-body">
        <?php if (empty($contacts)): ?>
            <div class="text-center py-5">
                <i class="fas fa-address-book fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">لا توجد جهات اتصال</h5>
                <?php if (hasPermission('debts_manage')): ?>
                    <a href="add.php" class="btn btn-primary mt-2">
                        <i class="fas fa-plus me-1"></i>
                        إضافة جهة اتصال جديدة
                    </a>
                <?php endif; ?>
            </div>
        <?php else: ?>
            <div class="table-responsive">
                <table class="table table-hover" id="contacts-table">
                    <thead>
                        <tr>
                            <th>الاسم</th>
                            <th>النوع</th>
                            <th>الهاتف</th>
                            <th>البريد الإلكتروني</th>
                            <th>عدد الديون</th>
                            <th>المبلغ المستحق</th>
                            <?php if ($_SESSION['user_role'] === 'admin'): ?>
                                <th>المكتب</th>
                            <?php endif; ?>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($contacts as $contact): ?>
                        <tr>
                            <td>
                                <div>
                                    <strong><?php echo htmlspecialchars($contact['name']); ?></strong>
                                    <?php if ($contact['address']): ?>
                                        <br><small class="text-muted"><?php echo htmlspecialchars(substr($contact['address'], 0, 50)); ?></small>
                                    <?php endif; ?>
                                </div>
                            </td>
                            <td>
                                <span class="badge bg-<?php echo $contact['type'] == 'customer' ? 'primary' : 'info'; ?>">
                                    <?php echo CONTACT_TYPES[$contact['type']]; ?>
                                </span>
                            </td>
                            <td>
                                <?php if ($contact['phone']): ?>
                                    <a href="tel:<?php echo $contact['phone']; ?>" class="text-decoration-none">
                                        <?php echo htmlspecialchars($contact['phone']); ?>
                                    </a>
                                <?php else: ?>
                                    <span class="text-muted">غير محدد</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <?php if ($contact['email']): ?>
                                    <a href="mailto:<?php echo $contact['email']; ?>" class="text-decoration-none">
                                        <?php echo htmlspecialchars($contact['email']); ?>
                                    </a>
                                <?php else: ?>
                                    <span class="text-muted">غير محدد</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <?php if ($contact['debts_count'] > 0): ?>
                                    <a href="../debts/index.php?search=<?php echo urlencode($contact['name']); ?>" class="badge bg-warning text-decoration-none">
                                        <?php echo number_format($contact['debts_count']); ?>
                                    </a>
                                <?php else: ?>
                                    <span class="badge bg-light text-dark">0</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <?php if ($contact['outstanding_amount'] > 0): ?>
                                    <strong class="text-danger">
                                        <?php echo formatCurrency($contact['outstanding_amount']); ?>
                                    </strong>
                                <?php else: ?>
                                    <span class="text-success">0.00</span>
                                <?php endif; ?>
                            </td>
                            <?php if ($_SESSION['user_role'] === 'admin'): ?>
                                <td><?php echo htmlspecialchars($contact['office_name']); ?></td>
                            <?php endif; ?>
                            <td>
                                <div class="btn-group btn-group-sm">
                                    <a href="view.php?id=<?php echo $contact['id']; ?>" class="btn btn-outline-info" title="عرض">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <?php if (hasPermission('debts_manage')): ?>
                                        <a href="edit.php?id=<?php echo $contact['id']; ?>" class="btn btn-outline-primary" title="تعديل">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a href="../debts/add.php?contact_id=<?php echo $contact['id']; ?>" class="btn btn-outline-success" title="إضافة دين">
                                            <i class="fas fa-plus"></i>
                                        </a>
                                        <a href="delete.php?id=<?php echo $contact['id']; ?>" class="btn btn-outline-danger" 
                                           title="حذف" onclick="return confirmDelete('هل أنت متأكد من حذف جهة الاتصال هذه؟')">
                                            <i class="fas fa-trash"></i>
                                        </a>
                                    <?php endif; ?>
                                </div>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
            
            <!-- Pagination -->
            <?php if ($total_pages > 1): ?>
                <nav aria-label="Page navigation" class="mt-4">
                    <ul class="pagination justify-content-center">
                        <?php if ($page > 1): ?>
                            <li class="page-item">
                                <a class="page-link" href="?page=<?php echo $page - 1; ?>&<?php echo http_build_query($_GET); ?>">السابق</a>
                            </li>
                        <?php endif; ?>
                        
                        <?php for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++): ?>
                            <li class="page-item <?php echo $i == $page ? 'active' : ''; ?>">
                                <a class="page-link" href="?page=<?php echo $i; ?>&<?php echo http_build_query($_GET); ?>"><?php echo $i; ?></a>
                            </li>
                        <?php endfor; ?>
                        
                        <?php if ($page < $total_pages): ?>
                            <li class="page-item">
                                <a class="page-link" href="?page=<?php echo $page + 1; ?>&<?php echo http_build_query($_GET); ?>">التالي</a>
                            </li>
                        <?php endif; ?>
                    </ul>
                </nav>
            <?php endif; ?>
        <?php endif; ?>
    </div>
</div>

<?php include '../../includes/footer.php'; ?>
