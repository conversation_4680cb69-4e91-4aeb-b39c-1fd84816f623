<?php
/**
 * اختبار قاعدة البيانات
 */

echo "<h1>اختبار قاعدة البيانات</h1>";

try {
    require_once 'config/database.php';
    
    echo "<h2>1. اختبار الاتصال:</h2>";
    $database = new Database();
    $db = $database->getConnection();
    
    if ($db) {
        echo "✅ الاتصال بقاعدة البيانات نجح<br>";
        
        echo "<h2>2. اختبار الجداول:</h2>";
        $tables = ['users', 'offices', 'contacts', 'debts'];
        
        foreach ($tables as $table) {
            try {
                $stmt = $db->query("SELECT COUNT(*) FROM $table");
                $count = $stmt->fetchColumn();
                echo "✅ جدول $table موجود ($count سجل)<br>";
            } catch (Exception $e) {
                echo "❌ جدول $table: " . $e->getMessage() . "<br>";
            }
        }
        
        echo "<h2>3. اختبار إدراج مستخدم:</h2>";
        
        // حذف المستخدم إذا كان موجوداً
        try {
            $database->delete('users', 'username = ?', ['test_user']);
            echo "تم حذف المستخدم التجريبي السابق<br>";
        } catch (Exception $e) {
            // لا مشكلة إذا لم يكن موجوداً
        }
        
        // إدراج مستخدم تجريبي
        try {
            $user_data = [
                'username' => 'test_user',
                'email' => '<EMAIL>',
                'password' => password_hash('test123', PASSWORD_DEFAULT),
                'full_name' => 'مستخدم تجريبي',
                'role' => 'admin',
                'office_id' => 1
            ];
            
            $user_id = $database->insert('users', $user_data);
            echo "✅ تم إدراج المستخدم التجريبي بنجاح (ID: $user_id)<br>";
            
            // اختبار التحديث
            $update_data = [
                'full_name' => 'مستخدم تجريبي محدث'
            ];
            
            $database->update('users', $update_data, 'id = ?', [$user_id]);
            echo "✅ تم تحديث المستخدم بنجاح<br>";
            
            // اختبار القراءة
            $user = $database->fetch('SELECT * FROM users WHERE id = ?', [$user_id]);
            if ($user) {
                echo "✅ تم قراءة بيانات المستخدم: " . $user['full_name'] . "<br>";
            }
            
            // حذف المستخدم التجريبي
            $database->delete('users', 'id = ?', [$user_id]);
            echo "✅ تم حذف المستخدم التجريبي<br>";
            
        } catch (Exception $e) {
            echo "❌ خطأ في اختبار المستخدم: " . $e->getMessage() . "<br>";
        }
        
        echo "<h2>4. النتيجة:</h2>";
        echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; color: #155724;'>";
        echo "✅ <strong>قاعدة البيانات تعمل بشكل صحيح!</strong><br>";
        echo "يمكنك الآن استكمال إعداد النظام.";
        echo "</div>";
        
    } else {
        echo "❌ فشل الاتصال بقاعدة البيانات<br>";
    }
    
} catch (Exception $e) {
    echo "❌ خطأ: " . $e->getMessage() . "<br>";
}

echo "<hr>";
echo "<h3>الخطوات التالية:</h3>";
echo "<a href='setup.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>إعداد النظام</a>";
echo " ";
echo "<a href='login.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>تسجيل الدخول</a>";
?>
