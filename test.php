<?php
/**
 * ملف اختبار النظام
 * System Test File
 */

echo "<h1>اختبار نظام الإدارة المالية</h1>";
echo "<hr>";

// اختبار إصدار PHP
echo "<h3>1. اختبار PHP:</h3>";
echo "إصدار PHP: " . PHP_VERSION . "<br>";
if (version_compare(PHP_VERSION, '7.4.0', '>=')) {
    echo "<span style='color: green;'>✓ إصدار PHP مناسب</span><br>";
} else {
    echo "<span style='color: red;'>✗ إصدار PHP قديم (مطلوب 7.4+)</span><br>";
}

// اختبار الإضافات المطلوبة
echo "<h3>2. اختبار الإضافات:</h3>";
$required_extensions = ['pdo', 'pdo_mysql', 'json', 'session'];
foreach ($required_extensions as $ext) {
    if (extension_loaded($ext)) {
        echo "<span style='color: green;'>✓ $ext مثبت</span><br>";
    } else {
        echo "<span style='color: red;'>✗ $ext غير مثبت</span><br>";
    }
}

// اختبار الملفات
echo "<h3>3. اختبار الملفات:</h3>";
$required_files = [
    'config/config.php',
    'config/database.php',
    'database/schema.sql',
    'includes/auth.php',
    'includes/header.php',
    'includes/footer.php'
];

foreach ($required_files as $file) {
    if (file_exists($file)) {
        echo "<span style='color: green;'>✓ $file موجود</span><br>";
    } else {
        echo "<span style='color: red;'>✗ $file غير موجود</span><br>";
    }
}

// اختبار قاعدة البيانات
echo "<h3>4. اختبار قاعدة البيانات:</h3>";
if (file_exists('config/database.php')) {
    try {
        require_once 'config/database.php';
        $database = new Database();
        $db = $database->getConnection();
        
        if ($db) {
            echo "<span style='color: green;'>✓ الاتصال بقاعدة البيانات نجح</span><br>";
            
            // اختبار الجداول
            $tables = ['users', 'offices', 'contacts', 'debts', 'cashbox_transactions', 'bank_accounts'];
            foreach ($tables as $table) {
                try {
                    $stmt = $db->query("SELECT COUNT(*) FROM $table");
                    $count = $stmt->fetchColumn();
                    echo "<span style='color: green;'>✓ جدول $table موجود ($count سجل)</span><br>";
                } catch (Exception $e) {
                    echo "<span style='color: red;'>✗ جدول $table غير موجود</span><br>";
                }
            }
        } else {
            echo "<span style='color: red;'>✗ فشل الاتصال بقاعدة البيانات</span><br>";
        }
    } catch (Exception $e) {
        echo "<span style='color: red;'>✗ خطأ في قاعدة البيانات: " . $e->getMessage() . "</span><br>";
    }
} else {
    echo "<span style='color: red;'>✗ ملف إعدادات قاعدة البيانات غير موجود</span><br>";
}

// اختبار الصلاحيات
echo "<h3>5. اختبار صلاحيات الملفات:</h3>";
$writable_dirs = ['config', 'assets', 'modules'];
foreach ($writable_dirs as $dir) {
    if (is_dir($dir) && is_writable($dir)) {
        echo "<span style='color: green;'>✓ مجلد $dir قابل للكتابة</span><br>";
    } else {
        echo "<span style='color: orange;'>⚠ مجلد $dir غير قابل للكتابة</span><br>";
    }
}

echo "<hr>";
echo "<h3>الخطوات التالية:</h3>";
echo "<ul>";
echo "<li><a href='setup.php'>تشغيل معالج الإعداد</a></li>";
echo "<li><a href='login.php'>صفحة تسجيل الدخول</a></li>";
echo "<li><a href='index.php'>الصفحة الرئيسية</a></li>";
echo "</ul>";

echo "<hr>";
echo "<p><strong>ملاحظة:</strong> إذا كانت جميع الاختبارات ناجحة، يمكنك حذف هذا الملف (test.php) لأسباب أمنية.</p>";
?>

<style>
body {
    font-family: Arial, sans-serif;
    margin: 20px;
    background-color: #f5f5f5;
}
h1, h3 {
    color: #333;
}
hr {
    border: 1px solid #ddd;
}
a {
    color: #007bff;
    text-decoration: none;
}
a:hover {
    text-decoration: underline;
}
</style>
