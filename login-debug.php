<?php
/**
 * تشخيص شامل لمشكلة تسجيل الدخول
 */

// تفعيل عرض الأخطاء
ini_set('display_errors', 1);
error_reporting(E_ALL);

echo "<h1>🔍 تشخيص شامل لمشكلة تسجيل الدخول</h1>";

$username = $_POST['username'] ?? 'admin';
$password = $_POST['password'] ?? 'password';
$debug_info = [];

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    echo "<div style='background: #e7f3ff; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
    echo "<h2>🧪 نتائج التشخيص التفصيلي:</h2>";
    
    try {
        // 1. اختبار الاتصال بقاعدة البيانات
        echo "<h3>1️⃣ اختبار الاتصال بقاعدة البيانات:</h3>";
        
        $host = 'localhost';
        $dbname = 'financial_management';
        $db_username = 'root';
        $db_password = '';
        
        $dsn = "mysql:host=$host;dbname=$dbname;charset=utf8mb4";
        $pdo = new PDO($dsn, $db_username, $db_password, [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION
        ]);
        echo "✅ الاتصال بقاعدة البيانات نجح<br>";
        
        // 2. البحث عن المستخدم
        echo "<h3>2️⃣ البحث عن المستخدم:</h3>";
        echo "البحث عن المستخدم: <strong>" . htmlspecialchars($username) . "</strong><br>";
        
        $sql = "SELECT * FROM users WHERE username = ? AND is_active = 1";
        $stmt = $pdo->prepare($sql);
        $stmt->execute([$username]);
        $user = $stmt->fetch();
        
        if ($user) {
            echo "✅ تم العثور على المستخدم<br>";
            echo "📋 تفاصيل المستخدم:<br>";
            echo "&nbsp;&nbsp;- ID: " . $user['id'] . "<br>";
            echo "&nbsp;&nbsp;- اسم المستخدم: " . htmlspecialchars($user['username']) . "<br>";
            echo "&nbsp;&nbsp;- الاسم الكامل: " . htmlspecialchars($user['full_name']) . "<br>";
            echo "&nbsp;&nbsp;- البريد الإلكتروني: " . htmlspecialchars($user['email']) . "<br>";
            echo "&nbsp;&nbsp;- الدور: " . htmlspecialchars($user['role']) . "<br>";
            echo "&nbsp;&nbsp;- المكتب: " . $user['office_id'] . "<br>";
            echo "&nbsp;&nbsp;- نشط: " . ($user['is_active'] ? 'نعم' : 'لا') . "<br>";
            
            // 3. اختبار كلمة المرور
            echo "<h3>3️⃣ اختبار كلمة المرور:</h3>";
            echo "كلمة المرور المدخلة: <strong>" . htmlspecialchars($password) . "</strong><br>";
            echo "كلمة المرور المشفرة في قاعدة البيانات:<br>";
            echo "<small style='word-break: break-all; background: #f8f9fa; padding: 5px;'>" . htmlspecialchars($user['password']) . "</small><br>";
            
            // اختبار التحقق من كلمة المرور
            $password_valid = password_verify($password, $user['password']);
            
            if ($password_valid) {
                echo "✅ <strong style='color: green;'>كلمة المرور صحيحة!</strong><br>";
                
                // 4. اختبار إنشاء الجلسة
                echo "<h3>4️⃣ اختبار إنشاء الجلسة:</h3>";
                
                if (session_status() === PHP_SESSION_NONE) {
                    session_start();
                }
                
                $_SESSION['user_id'] = $user['id'];
                $_SESSION['username'] = $user['username'];
                $_SESSION['full_name'] = $user['full_name'];
                $_SESSION['user_role'] = $user['role'];
                $_SESSION['office_id'] = $user['office_id'];
                $_SESSION['last_activity'] = time();
                $_SESSION['login_time'] = time();
                
                echo "✅ تم إنشاء الجلسة بنجاح<br>";
                echo "📋 بيانات الجلسة:<br>";
                echo "&nbsp;&nbsp;- معرف المستخدم: " . $_SESSION['user_id'] . "<br>";
                echo "&nbsp;&nbsp;- اسم المستخدم: " . $_SESSION['username'] . "<br>";
                echo "&nbsp;&nbsp;- الاسم الكامل: " . $_SESSION['full_name'] . "<br>";
                echo "&nbsp;&nbsp;- الدور: " . $_SESSION['user_role'] . "<br>";
                echo "&nbsp;&nbsp;- المكتب: " . $_SESSION['office_id'] . "<br>";
                
                echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 15px 0; color: #155724;'>";
                echo "<h4>🎉 تسجيل الدخول نجح بالكامل!</h4>";
                echo "<p>جميع الاختبارات نجحت. يمكنك الآن الدخول إلى النظام.</p>";
                echo "<a href='dashboard.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>دخول إلى لوحة التحكم</a>";
                echo "<a href='login.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>صفحة تسجيل الدخول</a>";
                echo "</div>";
                
            } else {
                echo "❌ <strong style='color: red;'>كلمة المرور خاطئة!</strong><br>";
                
                // اختبار كلمات مرور مختلفة
                echo "<h3>🔧 اختبار كلمات مرور مختلفة:</h3>";
                $test_passwords = ['password', 'admin', '123456', 'admin123', ''];
                
                foreach ($test_passwords as $test_pass) {
                    $test_result = password_verify($test_pass, $user['password']);
                    $status = $test_result ? '✅' : '❌';
                    echo "$status كلمة المرور: '<strong>$test_pass</strong>'<br>";
                    
                    if ($test_result) {
                        echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; color: #155724; margin: 10px 0;'>";
                        echo "🎯 <strong>كلمة المرور الصحيحة هي: '$test_pass'</strong>";
                        echo "</div>";
                        break;
                    }
                }
                
                // إعادة تعيين كلمة المرور
                echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 15px 0; color: #856404;'>";
                echo "<h4>🔧 إعادة تعيين كلمة المرور:</h4>";
                echo "<p>سأقوم بإعادة تعيين كلمة المرور إلى 'password'</p>";
                
                $new_hash = password_hash('password', PASSWORD_DEFAULT);
                $update_sql = "UPDATE users SET password = ? WHERE id = ?";
                $update_stmt = $pdo->prepare($update_sql);
                $update_result = $update_stmt->execute([$new_hash, $user['id']]);
                
                if ($update_result) {
                    echo "✅ تم إعادة تعيين كلمة المرور بنجاح!<br>";
                    echo "<strong>البيانات الجديدة:</strong><br>";
                    echo "اسم المستخدم: <strong>$username</strong><br>";
                    echo "كلمة المرور: <strong>password</strong><br>";
                    echo "<a href='login-debug.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>اختبار مرة أخرى</a>";
                } else {
                    echo "❌ فشل في إعادة تعيين كلمة المرور<br>";
                }
                echo "</div>";
            }
            
        } else {
            echo "❌ لم يتم العثور على المستخدم<br>";
            
            // عرض جميع المستخدمين
            echo "<h3>👥 المستخدمون الموجودون:</h3>";
            $all_users_sql = "SELECT username, full_name, role, is_active FROM users";
            $all_users_stmt = $pdo->query($all_users_sql);
            $all_users = $all_users_stmt->fetchAll();
            
            if ($all_users) {
                echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
                echo "<tr style='background: #f8f9fa;'><th style='padding: 10px;'>اسم المستخدم</th><th style='padding: 10px;'>الاسم الكامل</th><th style='padding: 10px;'>الدور</th><th style='padding: 10px;'>نشط</th></tr>";
                foreach ($all_users as $u) {
                    echo "<tr>";
                    echo "<td style='padding: 10px;'><strong>" . htmlspecialchars($u['username']) . "</strong></td>";
                    echo "<td style='padding: 10px;'>" . htmlspecialchars($u['full_name']) . "</td>";
                    echo "<td style='padding: 10px;'>" . htmlspecialchars($u['role']) . "</td>";
                    echo "<td style='padding: 10px;'>" . ($u['is_active'] ? '✅' : '❌') . "</td>";
                    echo "</tr>";
                }
                echo "</table>";
            } else {
                echo "لا توجد مستخدمين في قاعدة البيانات<br>";
                echo "<a href='setup.php' style='background: #dc3545; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>إعداد النظام</a>";
            }
        }
        
    } catch (Exception $e) {
        echo "❌ خطأ: " . htmlspecialchars($e->getMessage()) . "<br>";
    }
    
    echo "</div>";
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تشخيص تسجيل الدخول</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f8f9fa; }
        .container { max-width: 1000px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .form-group { margin-bottom: 20px; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input[type="text"], input[type="password"] { width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px; font-size: 16px; }
        .btn { padding: 12px 24px; border: none; border-radius: 5px; cursor: pointer; text-decoration: none; display: inline-block; margin: 5px; font-size: 16px; }
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        th, td { padding: 10px; text-align: right; border: 1px solid #ddd; }
        th { background: #f8f9fa; }
    </style>
</head>
<body>
    <div class="container">
        <h2>🔐 نموذج اختبار تسجيل الدخول</h2>
        
        <form method="POST">
            <div class="form-group">
                <label for="username">اسم المستخدم:</label>
                <input type="text" id="username" name="username" value="<?php echo htmlspecialchars($username); ?>">
            </div>
            
            <div class="form-group">
                <label for="password">كلمة المرور:</label>
                <input type="text" id="password" name="password" value="<?php echo htmlspecialchars($password); ?>">
                <small style="color: #666;">جرب: password, admin, 123456</small>
            </div>
            
            <button type="submit" class="btn btn-primary">🔍 تشخيص تسجيل الدخول</button>
        </form>
        
        <hr>
        
        <h3>🔗 روابط سريعة:</h3>
        <a href="fix-password.php" class="btn btn-success">إصلاح كلمة المرور</a>
        <a href="login.php" class="btn btn-primary">صفحة تسجيل الدخول</a>
        <a href="setup.php" class="btn" style="background: #6c757d; color: white;">إعداد النظام</a>
        
        <div style="background: #e7f3ff; padding: 15px; border-radius: 5px; margin: 20px 0;">
            <h4>💡 كيفية استخدام هذه الأداة:</h4>
            <ol>
                <li>أدخل اسم المستخدم وكلمة المرور</li>
                <li>اضغط "تشخيص تسجيل الدخول"</li>
                <li>ستحصل على تشخيص مفصل للمشكلة</li>
                <li>إذا كانت كلمة المرور خاطئة، ستتم إعادة تعيينها تلقائياً</li>
                <li>جرب تسجيل الدخول مرة أخرى</li>
            </ol>
        </div>
    </div>
</body>
</html>
