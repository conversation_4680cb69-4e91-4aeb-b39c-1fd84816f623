<?php
// تفعيل عرض الأخطاء
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

echo "<h1>تشخيص شامل للمشكلة</h1>";

echo "<h2>1. معلومات PHP الأساسية:</h2>";
echo "إصدار PHP: " . PHP_VERSION . "<br>";
echo "نظام التشغيل: " . PHP_OS . "<br>";
echo "الخادم: " . $_SERVER['SERVER_SOFTWARE'] . "<br>";

echo "<h2>2. اختبار الإضافات:</h2>";
$extensions = ['pdo', 'pdo_mysql', 'json', 'session', 'mbstring'];
foreach ($extensions as $ext) {
    $status = extension_loaded($ext) ? 'مثبت ✅' : 'غير مثبت ❌';
    echo "$ext: $status<br>";
}

echo "<h2>3. اختبار الملفات:</h2>";
$files = [
    'config/config.php',
    'config/database.php', 
    'includes/auth.php',
    'database/schema.sql',
    'login.php'
];

foreach ($files as $file) {
    if (file_exists($file)) {
        echo "$file: موجود ✅<br>";
        
        // اختبار قراءة الملف
        if (is_readable($file)) {
            echo "&nbsp;&nbsp;- قابل للقراءة ✅<br>";
        } else {
            echo "&nbsp;&nbsp;- غير قابل للقراءة ❌<br>";
        }
        
        // حجم الملف
        $size = filesize($file);
        echo "&nbsp;&nbsp;- الحجم: $size بايت<br>";
        
    } else {
        echo "$file: غير موجود ❌<br>";
    }
}

echo "<h2>4. اختبار المجلدات:</h2>";
$dirs = ['config', 'includes', 'modules', 'assets', 'database'];
foreach ($dirs as $dir) {
    if (is_dir($dir)) {
        echo "$dir: موجود ✅<br>";
        if (is_writable($dir)) {
            echo "&nbsp;&nbsp;- قابل للكتابة ✅<br>";
        } else {
            echo "&nbsp;&nbsp;- غير قابل للكتابة ⚠️<br>";
        }
    } else {
        echo "$dir: غير موجود ❌<br>";
    }
}

echo "<h2>5. اختبار تضمين الملفات:</h2>";

// اختبار config.php
echo "اختبار config/config.php:<br>";
if (file_exists('config/config.php')) {
    try {
        ob_start();
        include_once 'config/config.php';
        $output = ob_get_clean();
        echo "&nbsp;&nbsp;- تم تحميله بنجاح ✅<br>";
        if (!empty($output)) {
            echo "&nbsp;&nbsp;- تحذير: الملف يحتوي على مخرجات: " . htmlspecialchars($output) . "<br>";
        }
    } catch (Exception $e) {
        echo "&nbsp;&nbsp;- خطأ: " . $e->getMessage() . " ❌<br>";
    } catch (ParseError $e) {
        echo "&nbsp;&nbsp;- خطأ في الصيغة: " . $e->getMessage() . " ❌<br>";
    } catch (Error $e) {
        echo "&nbsp;&nbsp;- خطأ فادح: " . $e->getMessage() . " ❌<br>";
    }
} else {
    echo "&nbsp;&nbsp;- الملف غير موجود ❌<br>";
}

// اختبار database.php
echo "اختبار config/database.php:<br>";
if (file_exists('config/database.php')) {
    try {
        ob_start();
        include_once 'config/database.php';
        $output = ob_get_clean();
        echo "&nbsp;&nbsp;- تم تحميله بنجاح ✅<br>";
        if (!empty($output)) {
            echo "&nbsp;&nbsp;- تحذير: الملف يحتوي على مخرجات: " . htmlspecialchars($output) . "<br>";
        }
        
        if (class_exists('Database')) {
            echo "&nbsp;&nbsp;- فئة Database موجودة ✅<br>";
        } else {
            echo "&nbsp;&nbsp;- فئة Database غير موجودة ❌<br>";
        }
    } catch (Exception $e) {
        echo "&nbsp;&nbsp;- خطأ: " . $e->getMessage() . " ❌<br>";
    } catch (ParseError $e) {
        echo "&nbsp;&nbsp;- خطأ في الصيغة: " . $e->getMessage() . " ❌<br>";
    } catch (Error $e) {
        echo "&nbsp;&nbsp;- خطأ فادح: " . $e->getMessage() . " ❌<br>";
    }
} else {
    echo "&nbsp;&nbsp;- الملف غير موجود ❌<br>";
}

echo "<h2>6. متغيرات الخادم:</h2>";
echo "DOCUMENT_ROOT: " . ($_SERVER['DOCUMENT_ROOT'] ?? 'غير محدد') . "<br>";
echo "REQUEST_URI: " . ($_SERVER['REQUEST_URI'] ?? 'غير محدد') . "<br>";
echo "SCRIPT_NAME: " . ($_SERVER['SCRIPT_NAME'] ?? 'غير محدد') . "<br>";

echo "<h2>7. الروابط للاختبار:</h2>";
echo '<a href="hello.php">اختبار PHP بسيط</a><br>';
echo '<a href="basic.html">اختبار HTML</a><br>';
echo '<a href="setup.php">معالج الإعداد</a><br>';
echo '<a href="login.php">صفحة تسجيل الدخول</a><br>';

echo "<hr>";
echo "<p><strong>إذا رأيت هذه الصفحة، فإن PHP يعمل بشكل صحيح!</strong></p>";
?>
