<?php
/**
 * إعدادات النظام العامة
 * System Configuration
 */

// بدء الجلسة
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// إعدادات النظام
define('SITE_NAME', 'نظام الإدارة المالية');
define('SITE_URL', 'http://localhost/new2');
define('ADMIN_EMAIL', '<EMAIL>');

// إعدادات الأمان
define('SESSION_TIMEOUT', 3600); // ساعة واحدة
define('MAX_LOGIN_ATTEMPTS', 5);
define('LOGIN_LOCKOUT_TIME', 900); // 15 دقيقة

// إعدادات التطبيق
define('RECORDS_PER_PAGE', 20);
define('DATE_FORMAT', 'Y-m-d');
define('DATETIME_FORMAT', 'Y-m-d H:i:s');
define('CURRENCY_SYMBOL', 'ر.س');

// أدوار المستخدمين
define('USER_ROLES', [
    'admin' => 'مدير النظام',
    'manager' => 'مدير',
    'accountant' => 'محاسب',
    'viewer' => 'مراقب'
]);

// صلاحيات الأدوار
define('ROLE_PERMISSIONS', [
    'admin' => [
        'users_manage', 'offices_manage', 'debts_manage', 'cashbox_manage',
        'banks_manage', 'reports_view', 'settings_manage', 'logs_view'
    ],
    'manager' => [
        'debts_manage', 'cashbox_manage', 'banks_manage', 'reports_view',
        'users_view'
    ],
    'accountant' => [
        'debts_manage', 'cashbox_view', 'banks_view', 'reports_view'
    ],
    'viewer' => [
        'debts_view', 'cashbox_view', 'banks_view', 'reports_view'
    ]
]);

// أنواع المعاملات
define('TRANSACTION_TYPES', [
    'income' => 'دخل',
    'expense' => 'مصروف'
]);

// طرق الدفع
define('PAYMENT_METHODS', [
    'cash' => 'نقدي',
    'bank_transfer' => 'تحويل بنكي',
    'check' => 'شيك'
]);

// حالات الديون
define('DEBT_STATUS', [
    'pending' => 'معلق',
    'partial' => 'مدفوع جزئياً',
    'paid' => 'مدفوع',
    'overdue' => 'متأخر'
]);

// أنواع جهات الاتصال
define('CONTACT_TYPES', [
    'customer' => 'عميل',
    'supplier' => 'مورد'
]);

// إعدادات التاريخ والوقت
date_default_timezone_set('Asia/Riyadh');

/**
 * دالة للتحقق من صلاحية المستخدم
 */
function hasPermission($permission) {
    if (!isset($_SESSION['user_role'])) {
        return false;
    }
    
    $userRole = $_SESSION['user_role'];
    $permissions = ROLE_PERMISSIONS[$userRole] ?? [];
    
    return in_array($permission, $permissions);
}

/**
 * دالة لتنسيق التاريخ
 */
function formatDate($date, $format = 'Y-m-d') {
    if (empty($date)) return '';
    return date($format, strtotime($date));
}

/**
 * دالة لتنسيق المبلغ
 */
function formatCurrency($amount) {
    return number_format($amount, 2) . ' ' . CURRENCY_SYMBOL;
}

/**
 * دالة لتسجيل النشاط
 */
function logActivity($action, $table = null, $recordId = null, $oldValues = null, $newValues = null) {
    global $database;
    
    if (!isset($_SESSION['user_id'])) return;
    
    $data = [
        'user_id' => $_SESSION['user_id'],
        'action' => $action,
        'table_name' => $table,
        'record_id' => $recordId,
        'old_values' => $oldValues ? json_encode($oldValues) : null,
        'new_values' => $newValues ? json_encode($newValues) : null,
        'ip_address' => $_SERVER['REMOTE_ADDR'] ?? null
    ];
    
    try {
        $database->insert('activity_log', $data);
    } catch (Exception $e) {
        error_log("خطأ في تسجيل النشاط: " . $e->getMessage());
    }
}

/**
 * دالة للتحقق من انتهاء الجلسة
 */
function checkSessionTimeout() {
    if (isset($_SESSION['last_activity'])) {
        if (time() - $_SESSION['last_activity'] > SESSION_TIMEOUT) {
            session_destroy();
            header('Location: login.php?timeout=1');
            exit();
        }
    }
    $_SESSION['last_activity'] = time();
}

/**
 * دالة لحماية الصفحات
 */
function requireLogin() {
    if (!isset($_SESSION['user_id'])) {
        header('Location: login.php');
        exit();
    }
    checkSessionTimeout();
}

/**
 * دالة لحماية الصفحات بصلاحية معينة
 */
function requirePermission($permission) {
    requireLogin();
    if (!hasPermission($permission)) {
        header('Location: index.php?error=no_permission');
        exit();
    }
}

/**
 * دالة لتنظيف البيانات
 */
function sanitize($data) {
    return htmlspecialchars(strip_tags(trim($data)));
}

/**
 * دالة للتحقق من صحة البريد الإلكتروني
 */
function validateEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL);
}

/**
 * دالة لإنشاء كلمة مرور مشفرة
 */
function hashPassword($password) {
    return password_hash($password, PASSWORD_DEFAULT);
}

/**
 * دالة للتحقق من كلمة المرور
 */
function verifyPassword($password, $hash) {
    return password_verify($password, $hash);
}
?>
