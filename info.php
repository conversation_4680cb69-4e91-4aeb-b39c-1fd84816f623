<?php
// ملف اختبار PHP بسيط جداً
echo "<h1>اختبار PHP</h1>";
echo "<p>إذا رأيت هذه الرسالة، فإن PHP يعمل بشكل صحيح.</p>";
echo "<p>إصدار PHP: " . PHP_VERSION . "</p>";
echo "<p>الوقت الحالي: " . date('Y-m-d H:i:s') . "</p>";

// اختبار الإضافات الأساسية
echo "<h2>الإضافات المثبتة:</h2>";
$extensions = ['pdo', 'pdo_mysql', 'json', 'session'];
foreach ($extensions as $ext) {
    $status = extension_loaded($ext) ? '✅ مثبت' : '❌ غير مثبت';
    echo "<p>$ext: $status</p>";
}

// اختبار الملفات
echo "<h2>الملفات الموجودة:</h2>";
$files = [
    'config/config.php',
    'config/database.php',
    'includes/auth.php',
    'database/schema.sql'
];

foreach ($files as $file) {
    $status = file_exists($file) ? '✅ موجود' : '❌ غير موجود';
    echo "<p>$file: $status</p>";
}

phpinfo();
?>
